<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Redirecting...</title>
  <script>
    window.onload = function () {
      var ua = navigator.userAgent || navigator.vendor || window.opera;
      if (/android/i.test(ua)) {
        window.location.href = "https://play.google.com/store/apps/details?id=com.camhr.app";
      } else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {
        window.location.href = "https://itunes.apple.com/cn/app/qq/id1442800498?mt=8";
      } else {
        window.location.href = "http://fir.mosainet.com/camhrforandroid";
      }
    };
  </script>
</head>

<body>
  <p>正在跳转，请稍候...</p>
</body>

</html>