/**
 * 全局对象管理，维护全局只有一个
 * 1. nim 网易IM对象
 * 2. chatroom 聊天室
 * 3. sound 声音
 * 4. application 系统常量，从API加载
 */

const constant = {
  // 语言
  language: {
    'zh-CN': {
      display: '中文',
      code: 'zh',
    },
    'en-US': {
      display: 'English',
      code: 'en',
    },
    km: {
      display: 'ខែ្មរ', // 高棉语
      code: 'km',
    },
    'vi-VN': {
      display: 'Tiếng Việt', // 越南语
      code: 'vi',
    },
    'th-TH': {
      display: 'ภาษาไทย', // 泰语
      code: 'th',
    },
    'ko-KR': {
      display: '한국어로', // 韩语
      code: 'ko',
    },
  },
  languageMap: {
    en: 'en-US',
    zh: 'zh-CN',
    km: 'km',
    vi: 'vi',
    th: 'th',
    ko: 'ko-KR',
  },
  imLanguageMap: {
    en: 'en',
    zh: 'zh-CN',
    km: 'km-KH',
    vi: 'vi-VN',
    th: 'th-TH',
    ko: 'ko-KR',
  },
  nim: null,
  chatroom: null,
  sound: null,
  constantEnumZh: null,
  constantEnumEn: null,
  constantEnumKm: null,
  cache: {},
  defaultRegion: {
    id: 1,
    name: 'Cambodia',
    code: '855',
    label: '+855(0)',
    pattern: /^[1-9]\d{7,8}$/,
  },
  resumeFileType: {
    pdf: 'pdf',
    doc: 'doc',
    docx: 'docx',
    png: 'png',
    jpg: 'jpg',
  },
  resumeFileUploadType: {
    jpg: 'image/jpeg',
    png: 'image/png',
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  },
  resumeType: {
    online: 0, // 在线简历
    annex: 1, // 附件简历
  },
  // 订阅数据主题
  topic: {
    onlineResumeChanged: 'onlineResumeChanged', // 在线简历数据改变
  },
  event: {
    globalLoading: 'globalLoading', // 全局加载框
    globalAlert: 'globalAlert', // 全局对话框
    sessionTimeout: 'sessionTimeout',
    tabbarChanged: 'tabbarChanged',
    navigationStateChange: 'navigationStateChange',
    jobChanged: 'jobChanged',
    pendingTxChange: 'pendingTxChange',
    changeAddress: 'changeAddress', // 修改地址
    interviewStatusChanged: 'interviewStatusChanged', // 面试状态改变
    downloadResumeAdd: 'downloadResumeAdd', // 下载简历
    resumeStatusChanged: 'resumeStatusChanged', // 简历状态改变
    resumeJobChanged: 'resumeJobChanged', // 简历职位改变
    rechargeFilterDataChanged: 'rechargeFilterDataChanged', // 充值筛选数据改变
    rechargeTabChanged: 'rechargeTabChanged', // 充值Tab改变
    resumeFilterDataChanged: 'resumeFilterDataChanged', // 简历筛选数据改变
    resumeTabChanged: 'resumeTabChanged', // 简历Tab改变
    homeResumeFilterChanged: 'homeResumeFilterChanged', // 首页简历筛选数据改变
    servicePackageTabChanged: 'servicePackageTabChanged', // 服务包Tab改变
    rechargeSuccess: 'rechargeSuccess', // 充值成功
    imAccessTokenChange: 'imAccessTokenChange', // im token变更
    imSessionTimeout: 'imSessionTimeout', // 聊天token过期
    receiveMessage: 'receiveMessage', // 收到消息
    chatMessageRead: 'chatMessageRead', // 消息已读
    deleteContact: 'deleteContact', // 删除联系人
    addContact: 'addContact', // 添加联系人
    chatSessionChange: 'chatSessionChange', // 聊天会话有变化
    groupMemberChange: 'groupMemberChange', // 群成员变动通知
    groupRoleChange: 'groupRoleChange', // 群成员角色变动通知
    groupSettingChange: 'groupSettingChange', // 群管理设置项变动通知
    groupInfoChange: 'groupInfoChange', // 群信息变动通知
    quoteMessage: 'quoteMessage', // 引用/回复消息
    showQuoteMessageText: 'showQuoteMessageText', // 显示引用/回复的文本消息
    messageChange: 'messageChange', // 消息变动
    foldPanel: 'foldPanel', // 收起聊天更多功能面板
    playVideo: 'playVideo', // 播放视频
    blacklistChange: 'blacklistChange', // 黑名单变更
    blackMessage: 'blackMessage', // 收到黑名单消息
    viewChatImages: 'viewChatImages', // 查看聊天内的图片
    clearAllMessage: 'clearAllMessage', // 清空聊天记录
    showResumeModal: 'showResumeModal', // 显示简历列表弹窗
    showInviteModal: 'showInviteModal', // 显示邀请面试弹窗
    showSelectJobModal: 'showSelectJobModal', // 显示选择职位弹窗
    showActionSheet: 'showActionSheet', // 显示底部菜单弹窗
    showTelegramModal: 'showTelegramModal', // 显示Telegram账号输入弹窗
    updateQuickText: 'updateQuickText', // 更新快捷文本
    jobFollowChange: 'jobFollowChange', // 职位关注状态改变
    imConfigChange: 'imConfigChange', // im配置变更
  },
  // 主题
  theme: {
    night: 'night',
    daytime: 'daytime',
  },
  // tab 页类型
  tabs: {
    home: 'home',
    resume: 'resume',
    chat: 'chat',
    job: 'job',
    mine: 'mine',
  },
  tabPageBarStyles: {
    home: 'dark-content',
    resume: 'dark-content',
    chat: 'dark-content',
    job: 'dark-content',
    mine: 'light-content',
  },

  /* 聊天相关 */
  // 用户类型
  userType: {
    im: 0, // 仅聊天
    all: 1, // 聊天和钱包
    robot: 2, // 机器人
    accountant: 3, // 群收款人
    manage: 999, // 管理员
  },
  // 会话类型
  sessionType: {
    P2P: 0, // 私聊
    TEAM: 1, // 群聊
  },
  // 固定的会话类型
  fixedSessionId: {
    gpt: 1, // GPT聊天/机器人
  },
  // 消息类型
  messageType: {
    none: 'none',
    text: 'text',
    image: 'image',
    video: 'video',
    audio: 'audio',
    file: 'file',
    redPacket: 'redPacketLP', //发红包
    redPacketAccept: 'redPacketAcceptLP', //收红包
    transfer: 'transferLP', //转账
    groupTransfer: 'groupTransfer', //群转账
    transferReturn: 'transferReturnLP', //转账退回
    transferAccept: 'transferAcceptLP', //转账接收
    receiveCreate: 'receiveCreateLP', //发起收款
    receivePaid: 'receivePaidLP', //支付收款
    tip: 'tip',
    notice: 'notice',
    withdraw: 'withdraw', // 撤回消息
    referTo: 'referTo', // 艾特
    businessCard: 'businessCard', //个人名片
    newFriend: 'newFriend', //好友添加
    call: 'call', //语音视频通话
    audioCall: 'audioCall', //语音通话
    videoCall: 'videoCall', //视频通话
    meetingCall: 'meetingCall', //群聊音视频通话
    groupAnnouncement: 'groupAnnouncement', // 修改或者新增群公告

    job: 'job', // 职位，求职者查看职位时，点按聊天发送的消息类型
    resume: 'resume', // 简历，企业查看简历时，点按聊天发送的消息类型
    reqResume: 'reqResume', // 企业请求求职者的简历
    sendResume: 'sendResume', // 求职者发送简历
    inviteInterview: 'inviteInterview', // 企业发出邀请面试
    changeJob: 'changeJob', // 企业更换岗位
    companyBusinessCard: 'companyBusinessCard', // 公司名片
  },
  // 消息通知类型
  messageNoticeType: {
    onLinePing: 'OnLinePing', // 发送在线ping
    addContact: 'AddContact', // 添加好友
    AddedContact: 'AddedContact', // 已经添加好友
    agreeContact: 'AgreeContact', // 同意添加好友
    deleteContacts: 'DeleteContacts', // 删除联系人
    messageRead: 'MessageRead', // 消息已读
    messageArrived: 'MessageArrived', // 消息已送达

    groupInvite: 'group_invite', // 创建群，成员收到邀请通知
    groupRemove: 'group_remove', // 删除群成员通知
    groupDisbanded: 'group_disbanded', // 解散群通知
    groupRole: 'GroupRole', // 群成员角色变更通知
    modifyGroup: 'ModifyGroup', // 群信息修改通知，修改群名称等
    modifyMember: 'ModifyMember', // 群信息修改通知，修改群成员信息
    groupSetting: 'GroupSetting', // 群设置修改通知
    noSpeaking: 'NoSpeaking', // 群成员禁言通知

    addBlacklist: 'add_blacklist', // 添加黑名单
    rmBlacklist: 'rm_blacklist', // 移出黑名单
  },
  // 消息发送状态
  messageSendState: {
    sending: 0,
    successful: 1,
    failed: 2,
  },
  // 发送给服务器的事件类型
  messageAction: {
    msgP2P: 'msg_p2p',
    msgP2PAck: 'msg_p2p_ack',
    msgGroup: 'msg_group',
    msgGroupAck: 'msg_group_ack',
    heartbeat: 'heartbeat', // 发送心跳
    heartbeatAck: 'heartbeat_ack', // 心跳返回
    notice: 'notice',
  },
  imSocketState: {
    init: 'init', // 初始化，开始连接时
    connecting: 'connecting', // 连接中
    opened: 'opened', // 已打开
    connected: 'connected', // 已连接
    error: 'error', // 连接出错
    close: 'close', // 连接关闭
    reconnecting: 'reconnecting', // 等待重连
  },
  groupMemberRole: {
    owner: 'owner', // 群主
    admin: 'admin', // 管理员
    member: 'member', // 普通成员
  },
  messageTransferState: {
    wait: 1,
    finish: 2,
    return: -1,
    expired: -2,
  },
  // 无效的消息类型，过滤非法消息时返回对应类型
  invalidMsgType: {
    notGroup: 1, // 没有这个群 或 不是群成员
    noSpeakingGroup: 2, // 普通群成员禁言
    noSpeakingGroupMember: 3, // 指定的群成员禁言

    notFriend: 20, // 非好友
    blackedFriend: 21, // 黑名单好友 或 在对方黑名单

    onlySendNotSave: 50, // 非好友仅发送消息，不保留该消息，如给陌生人转账
  },
  // 音视频通话
  callType: {
    singleAudioCall: 1,
    singleVideoCall: 2,
    meetingCall: 3,
  },
  // 音视频通话状态
  callState: {
    none: 0,
    invite: 1,
    cancel: 2,
    receiveInvite: 3,
    accept: 4,
    reject: 5,
    noResponse: 6,
    end: 7,
    busy: 8,
    switch: 9,
  },
};

export default constant;
