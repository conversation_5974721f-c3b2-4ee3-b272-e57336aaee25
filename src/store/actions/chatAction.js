import { action, autorun, toJS } from 'mobx';
import ChatService from '../../api/chatService';
import userStore from '../stores/user';
import chatStore from '../stores/chatStore';
import settingsStore from '../stores/settings';
import friendDao from '../../database/dao/friendDao';
import chatMessageUtil from '../../database/chatMessageUtil';
import ImUserService from '../../api/imUserService';
import UserService from '../../api/userService';
import Session from '../../api/session';
import constant from '../constant';
import taskUtil from '../../util/taskUtil';
import chatSessionDao from '../../database/dao/chatSessionDao';
import ChatSession from '../../database/chatSession';
import I18n, { getAcceptLanguageByIM } from '../../i18n';
import navigationService from '../../navigationService';
import { AppState } from 'react-native';

/**
 * IM actions
 * action 统一返回一个promise
 * author: 盛宣伟
 */
class ChatAction {
  autoLoginIM = () => {
    this.autoLoginImDisposer?.();
    this.autoLoginImDisposer = autorun(() => {
      const { isLogin } = userStore;
      console.debug(`autoLoginIM isLogin:${isLogin}`);
      if (isLogin) {
        taskUtil.autoRetry('loginIM', this.loginIM);
      } else {
        taskUtil.removeAutoRetry('loginIM');
      }
    });
    this.autoUpdateImLangDisposer?.();
    this.autoUpdateImLangDisposer = autorun(() => {
      const { language } = settingsStore;
      console.debug(`autoLoginIM language:${language}`);
      taskUtil.autoRetry('updateImLang', this.updateImLang);
    });
  };

  loginIM = async () => {
    console.debug('loginIM start');
    global.emitter.emit(constant.event.imAccessTokenChange, null);

    let [imUser, loginUser] = await Promise.all([Session.getImLoginInfo(), Session.getUser()]);
    let reqLoginIm = true;
    try {
      console.debug('loginIM check', imUser, loginUser);
      // 检查本地IM登录信息是不是当前登录用户的，并刷新IM token
      if (imUser && imUser.accid === loginUser?.user?.accid) {
        const res = await ImUserService.refreshAuthToken();
        if (res.im_token) {
          await Session.setImAccessToken(res.im_token, res.im_exp);
        }
        reqLoginIm = false;
      } else {
        await Promise.all([Session.setImAccessToken(null), Session.setImLoginInfo(null)]);
      }
    } catch (e) {
      console.warn('refreshAuthToken error', e);
      // 本地token未过期，但接口报过期，则是在另一个设备登录了
      if (e?.code === -403) {
        const imToken = await Session.getImAccessToken();
        if (imToken?.isValid()) {
          global.emitter.emit(constant.event.imSessionTimeout, { isSocket: true });
          return;
        }
      }
    }

    if (reqLoginIm) {
      let res = await UserService.imLogin(userStore.isCompany);
      console.debug('loginIM res', res);
      if (!res.data?.im_token) {
        return Promise.reject({ message: 'IM Login Error' });
      }
      imUser = res.data.user;
      // 保存accid
      imUser.accid = loginUser?.user?.accid;
      await Promise.all([
        Session.setImAccessToken(res.data.im_token, res.data.im_exp),
        Session.setImLoginInfo(imUser),
      ]);
    } else {
      const r = await ImUserService.getImUserInfo(imUser.im_id);
      imUser = {
        ...imUser,
        ...r,
      };
      await Session.setImLoginInfo(imUser);
    }
    userStore.imLoginInfo = imUser;
    // 后端会同步用户头像和昵称
    /*const { avatar, userName } = userStore;
    const lang = getAcceptLanguageByIM();
    if (imUser.nickname !== userName || imUser.avatar !== avatar || imUser.lang !== lang) {
      await ImUserService.updateImUser(imUser.im_id, {
        nickname: userName,
        avatar,
        lang,
      });
      imUser.nickname = userName;
      imUser.avatar = avatar;
      imUser.lang = lang;
    }*/
    console.debug('loginIM has token');
    userStore.isLatestToken = true;
    taskUtil.autoRetry('checkAddGPTSession', this.checkAddGPTSession);
    await this.updateImLang();
  };

  // 企业登录IM
  loginIMByEmployee = async (data) => {
    console.debug('loginIMByEmployee start');
    global.emitter.emit(constant.event.imAccessTokenChange, null);
    let [loginUser] = await Promise.all([Session.getUser()]);

    let res = await UserService.imLoginByEmployee(data);
    let imUser;
    console.debug('loginIMByEmployee res', res);
    if (!res.data?.im_token) {
      return Promise.reject(res);
    }
    imUser = res.data.user;
    // 保存accid
    imUser.accid = loginUser?.user?.accid;
    await Promise.all([
      Session.setImAccessToken(res.data.im_token, res.data.im_exp),
      Session.setImLoginInfo(imUser),
    ]);

    userStore.imLoginInfo = imUser;

    console.debug('loginIMByEmployee has token');
    userStore.isLatestToken = true;
    await this.updateImLang();
  };

  updateImLang = async () => {
    const { hasConnectImSocket, imLoginInfo } = userStore;
    if (!hasConnectImSocket || !imLoginInfo) return;
    const lang = getAcceptLanguageByIM();
    if (imLoginInfo.lang === lang) return;
    await ImUserService.updateImUser(userStore.imId, {
      lang,
    });
    imLoginInfo.lang = lang;
    await Session.setImLoginInfo(toJS(imLoginInfo));
  };

  /**
   * 通过手机号搜索
   * @param {*} phone
   * @returns
   */
  @action
  searchContactByPhone = (phone) => {
    return ChatService.searchContactByPhone(phone);
  };

  /**
   * 获取指定用户信息，同步更新本地信息
   * @param imId
   * @param isLocal 优先拿本地的，本地没有则获取服务器的
   * @param isSave 是否保存到本地
   * @param isLimit 是否走限制查询接口
   * @param user
   * @return {Promise<{friend: Friend, user: Object}>}
   */
  getFriend = async (imId, isLocal, isSave, isLimit, user) => {
    let friend;
    if (isLocal) {
      friend = await friendDao.getFriend({ imId });
      if (!friend && !user) {
        user = await ChatService.searchContactByUserID(imId, isLimit);
      }
    } else {
      const values = [friendDao.getFriend({ imId })];
      if (!user) {
        values.push(ChatService.searchContactByUserID(imId, isLimit));
      }
      const res = await Promise.all(values);
      friend = res[0];
      if (!user) {
        user = res[1];
      }
    }
    if (!user) return { friend, user };
    // 是好友 或 本地存在，则保存更新
    isSave = isSave || user.relation_type === 1 || friend;
    friend = chatMessageUtil.toFriend(user, friend);
    if (isSave) {
      await friendDao.saveFriend(friend);
    }
    return { friend, user, existLocal: isSave };
  };

  /**
   * 通过用户id搜索
   */
  @action
  searchContactByUserID = async (imId, isLimit) => {
    const res = await this.getFriend(imId, undefined, undefined, isLimit);
    return res.user;
  };

  /**
   * 添加联系人
   */
  @action
  addContact = async (cid) => {
    const res = await ChatService.addContact(userStore.imId, cid);
    console.log('chatAction addContact', res);
    const contact = res.data?.contact || res.contact;
    await this.getFriend(cid, false, true, false, contact);
    return contact;
  };

  /**
   * 设置联系人tags分组
   */
  @action
  setContactTags = (data) => {
    return ChatService.setContactTags(userStore.imId, data);
  };

  /**
   * 添加联系人 (需要对方同意)
   */
  @action
  addContactReq = (params) => {
    return ChatService.addContactReq(userStore.imId, params);
  };

  /**
   * 删除联系人
   */
  @action
  deleteContact = (cid) => {
    return ChatService.deleteContact(userStore.imId, cid);
  };

  @action
  agreeContactReq = (req_id, params) => {
    return ChatService.agreeContactReq(userStore.imId, req_id, params);
  };

  /**
   * 通讯录列表排除
   */
  @action
  getFriends = async (isLocal, includeDeleted, excludeSelf) => {
    const { imId } = userStore;
    if (!imId) return [];

    let localFriends, remoteFriends;
    if (isLocal) {
      localFriends = await friendDao.getAllFriends({ ownerId: imId, excludeSelf });
      if (!localFriends.length) {
        remoteFriends = await ChatService.getContacts(imId);
      }
    } else {
      const [local, remote] = await Promise.all([
        friendDao.getAllFriends({ ownerId: imId, excludeSelf }),
        ChatService.getContacts(imId),
      ]);
      localFriends = local;
      remoteFriends = remote;
    }
    if (!remoteFriends) {
      return includeDeleted ? localFriends : localFriends.filter((item) => !item.beDeleted);
    }
    const localFriendMap = new Map();
    localFriends.forEach((item) => localFriendMap.set(item.imId, item));
    const result = {
      insert: [],
      update: [],
      delete: [],
    };
    remoteFriends.forEach((item) => {
      item.relation_type = 1;
      const friend = chatMessageUtil.toFriend(item, localFriendMap.get(item.im_id));
      if (friend.action === 'update') {
        result.update.push(friend);
        localFriendMap.delete(item.im_id);
      } else {
        result.insert.push(friend);
      }
    });
    result.delete = [...localFriendMap.values()];
    await friendDao.saveFriends({ data: result, ownerId: imId });
    return includeDeleted
      ? [...result.insert, ...result.update, ...result.delete]
      : [...result.insert, ...result.update];
  };

  handleFriendTags = (friends, imId) => {
    let friend = null;
    const map = new Map();
    friends.forEach((item) => {
      if (imId === item.imId) {
        friend = item;
      }
      if (!item.tags) return;
      const tags = JSON.parse(item.tags);
      item.tagArr = tags;
      tags.forEach((tag) => {
        let arr = map.get(tag);
        if (!arr) {
          arr = [item];
          map.set(tag, arr);
        } else if (arr.indexOf(item) === -1) {
          arr.push(item);
        }
      });
    });
    return { tags: [...map.keys()], tagMap: map, friend };
  };

  setCurrentSession = (session, memberMap, groupInfo) => {
    chatStore.currentSessionInfo = session && {
      session,
      memberMap,
      groupInfo,
    };
  };

  getCurrentSessionMember = (sessionId, imId, isGroup) => {
    const { currentSessionInfo } = chatStore;
    return (
      currentSessionInfo?.session?.sessionId === sessionId &&
      !!currentSessionInfo.session.isGroup === !!isGroup &&
      currentSessionInfo.memberMap?.get(imId)
    );
  };

  /**
   * 是否正在当前会话聊天窗口中，并且正在使用
   */
  isCurrentSessionAndActive = (session) => {
    return (
      navigationService.getCurrentScreen() === 'chatMessage' &&
      AppState.currentState === 'active' &&
      session?.sessionId &&
      chatStore.currentSession?.sessionId === session.sessionId
    );
  };

  /**
   * 检查添加GPT会话
   */
  checkAddGPTSession = async () => {
    return;
    const { imId, hasConnectImSocket } = userStore;
    console.debug('checkAddGPTSession', imId, hasConnectImSocket);
    if (!hasConnectImSocket) return;
    let enable = false;
    const res = await ChatService.isGPTEnabled();
    enable = res?.enable;
    // 不可用时不显示
    if (!enable) {
      await chatSessionDao.deleteSession({
        sessionId: constant.fixedSessionId.gpt,
        ownerId: imId,
      });
      return;
    }
    let s = await chatSessionDao.getChatSession({
      sessionId: constant.fixedSessionId.gpt,
      ownerId: imId,
    });
    if (!s) {
      const chatSession = new ChatSession();
      chatSession.sessionId = constant.fixedSessionId.gpt;
      chatSession.ownerId = imId;
      chatSession.title = I18n.t('page_chat_gpt_session_title');
      chatSession.setTopTime = Date.now(); // 设置个较大的时间，保证是第一个
      chatSession.lastMsgTime = Date.now();
      await chatSessionDao.addSessionByMsg(chatSession);
    }
  };
}

export default new ChatAction();
