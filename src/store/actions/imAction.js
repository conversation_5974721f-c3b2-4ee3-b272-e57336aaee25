import { action } from 'mobx';
import AliyunOSS from 'aliyun-oss-react-native';
import ImService from '../../api/imService';
import ImUserService from '../../api/imUserService';
import ChatMessage from '../../database/chatMessage';
import chatMessageUtil from '../../database/chatMessageUtil';
import messageDao from '../../database/dao/messageDao';
import chatSessionDao from '../../database/dao/chatSessionDao';
import constant from '../constant';
import ChatGroup from '../../database/chatGroup';
import userStore from '../stores/user';
import chatGroupDao from '../../database/dao/chatGroupDao';
import groupMemberDao from '../../database/dao/groupMemberDao';
import GroupMember from '../../database/groupMember';
import chatAction from './chatAction';
import callAction from './callAction';
import meetingAction from './meetingAction';
import ContactModel from '../../database/contactModel';
import contactDao from '../../database/dao/contactDao';
import sendMessageUtil from '../../database/sendMessageUtil';
import friendDao from '../../database/dao/friendDao';
import Session, { UserSettingKeys } from '../../api/session';
import chatStore from '../stores/chatStore';
import promiseUtil from '../../util/promiseUtil';
import receiveMessageUtil from '../../database/receiveMessageUtil';
import I18n from '../../i18n';
import navigationService from '../../navigationService';
import moment from 'moment';
import messageReadUtil from '../../database/messageReadUtil';
import taskUtil from '../../util/taskUtil';
import CollectionUtil from '../../util/collectionUtil';
import {
  getChatMessageTableName,
  getCreateGroupMemberTableSQL,
  getGroupMemberTableName,
} from '../../database/initSQL';
import sqliteService from '../../database/sqliteService';
import databaseUtil from '../../database/databaseUtil';
import deviceInfo from '../../util/deviceInfo';
import sentryUtil from '../../util/sentryUtil';
import ChatService from '../../api/chatService';
import imService from '../../api/imService';

if (__DEV__) {
  AliyunOSS.enableDevMode();
}

/**
 * IM actions
 * <AUTHOR>
 */
class ImAction {
  /**
   * 获取所有离线消息列表
   */
  getAllOfflineMessageList = () => {
    /*if (
      this.getAllOfflineMessageListTime &&
      Date.now() - this.getAllOfflineMessageListTime < 1000
    ) {
      console.log('getAllOfflineMessageList 操作太频繁');
      return;
    }
    this.getAllOfflineMessageListTime = Date.now();*/
    console.log('getAllOfflineMessageList 1');
    taskUtil.singleExec('_getAllOfflineMessageList', this._getAllOfflineMessageList);
  };

  /**
   * 获取所有离线消息列表，每隔30s会执行一次
   */
  _getAllOfflineMessageList = async () => {
    const time = Date.now();
    try {
      this.stopSyncOfflineDelay();
      if (!userStore.isLogin) {
        return;
      }
      console.log('imAction _getAllOfflineMessageList start');
      await ImUserService.getConfigByDHWL();
      const [res] = await Promise.all([
        ImUserService.getUserInfoRes(userStore.imId),
        // Session.getUserSettings(UserSettingKeys.imUserRes),
      ]);
      Session.setUserSettings(UserSettingKeys.imUserRes, res);
      // const tasks = [this.getGroupList(true), chatAction.getFriend(userStore.imId, true, true)];
      // if (res?.contacts?.list !== localRes?.contacts?.list) {
      //   tasks.push(chatAction.getFriends());
      // }

      // 刷新本地保存的管理员类型的用户，防止用户类型变更了，目前应该不会变更，暂时不刷新也可以
      // tasks.push(taskUtil.singleExec('refreshManageInfo', this.refreshManageInfo));

      // await Promise.all(tasks);

      // 优先获取Notice，里面可能包含用户、群设置等变更的消息，这里最多执行3秒
      await promiseUtil.raceByAllSettledWithTimeout(
        [
          taskUtil.singleExec('getOfflineNotice', this.getOfflineNotice),
          // taskUtil.singleExec('getGroupOfflineNotice', this.getGroupOfflineNotice),
        ],
        3000,
        false
      );

      taskUtil.singleExec('getOfflineMessageList', this.getOfflineMessageList);
      // taskUtil.singleExec('getGroupOfflineMessageList', this.getGroupOfflineMessageList);
      // taskUtil.singleExec('syncNotifications', this.syncNotifications);
      // taskUtil.singleExec('getSelfMessageList', this.getSelfMessageList);
      // 延迟3秒，避免频繁执行
      await promiseUtil.sleep(3000);
      this.startSyncOfflineDelay(30000);
    } catch (e) {
      console.warn('imAction _getAllOfflineMessageList error', e);
      this.startSyncOfflineDelay(3000);
    }
    console.log('imAction _getAllOfflineMessageList', Date.now() - time);
  };

  /**
   * 刷新本地保存的管理员类型的用户，防止用户类型变更了，目前应该不会变更，暂时不刷新也可以
   * @returns {Promise<void>}
   */
  refreshManageInfo = async () => {
    const time = Date.now();
    if (this.refreshManageInfoTime && time - this.refreshManageInfoTime < 3600000) return;
    this.refreshManageInfoTime = time;
    const list = await friendDao.getManageImIds();
    if (!list.length) return;
    const tasks = [];
    for (let it of list) {
      tasks.push(chatAction.getFriend(it.imId, false, true));
    }
    await Promise.all(tasks);
  };

  startSyncOfflineDelay = (time) => {
    this.stopSyncOfflineDelay();
    console.log('imAction startSyncOfflineDelay', time);
    this.syncOfflineDelayTask = setTimeout(this.getAllOfflineMessageList, time);
  };

  stopSyncOfflineDelay = () => {
    console.log('imAction stopSyncOfflineDelay');
    if (this.syncOfflineDelayTask) {
      clearTimeout(this.syncOfflineDelayTask);
      this.syncOfflineDelayTask = null;
    }
  };

  getOfflineNotice = async () => {
    return this._getOfflineNotice(false);
  };

  getGroupOfflineNotice = async () => {
    return this._getOfflineNotice(true);
  };

  _getOfflineNotice = async (isGroup) => {
    const param = { limit: 500 };
    while (true) {
      const res = await ImService.getOfflineNotice(param, isGroup);
      console.debug('ImAction _getOfflineNotice 1', isGroup, res);
      const lastNoticeId = await this.saveOfflineNoticeList(res, isGroup);
      console.debug('ImAction _getOfflineNotice 2', isGroup, lastNoticeId);
      await this.markReadNotice(lastNoticeId, isGroup);
      if (res.data?.length !== param.limit) {
        break;
      }
      console.debug('ImAction _getOfflineNotice again');
    }
  };

  saveOfflineNoticeList = async (res, isGroup) => {
    if (!res.data?.length) return null;
    const typeMap = new Map();
    const TypeKey = {
      refreshFriend: 'refreshFriend',
      refreshGroup: 'refreshGroup',
      refreshGroupMember: 'refreshGroupMember',
    };
    res.data.forEach((item) => {
      // 如果socket接收时已经处理成功，这里跳过
      if (chatMessageUtil.existNoticeId(item)) {
        return;
      }
      switch (item.notice.notice_type) {
        case constant.messageNoticeType.addBlacklist:
        case constant.messageNoticeType.rmBlacklist:
        case constant.messageNoticeType.deleteContacts:
          CollectionUtil.mapSetAdd(typeMap, TypeKey.refreshFriend, item.notice.data.im_id);
          break;
        case constant.messageNoticeType.groupRole:
        case constant.messageNoticeType.groupSetting:
        case constant.messageNoticeType.modifyGroup:
          CollectionUtil.mapSetAdd(typeMap, TypeKey.refreshGroup, item.notice.data.group_id);
          CollectionUtil.mapSetAdd(typeMap, TypeKey.refreshGroupMember, item.notice.data.group_id);
          break;
        case constant.messageNoticeType.modifyMember:
        case constant.messageNoticeType.noSpeaking:
          CollectionUtil.mapSetAdd(typeMap, TypeKey.refreshGroupMember, item.notice.data.group_id);
          break;
      }
    });

    let tasks = [];
    function addTasks(set, fun) {
      [...set].forEach((item) => {
        tasks.push(fun(item));
      });
    }

    typeMap.forEach((set, key) => {
      switch (key) {
        case TypeKey.refreshFriend:
          addTasks(set, (imId) => chatAction.getFriend(imId, false, true));
          break;
        case TypeKey.refreshGroup:
          addTasks(set, (groupId) => this.getGroupInfo(groupId));
          break;
        case TypeKey.refreshGroupMember:
          addTasks(set, (groupId) => this.getGroupMemberList(groupId));
          break;
      }
    });
    console.log('imAction saveOfflineNoticeList', isGroup, tasks.length);
    if (tasks.length) {
      await promiseUtil.allSettled(tasks);
    }

    return res.data[res.data.length - 1].notice_id;
  };

  /**
   * 获取私聊离线消息列表
   */
  getSelfMessageList = async (param, page = 1, setting, index) => {
    await promiseUtil.sleep(3000);

    if (param) {
      param = {
        // limit: 100,
        im_id: param.sessionId,
        // start_msg_id: 1,
        end_msg_id: Number.MAX_SAFE_INTEGER,
      };
      setting = await Session.getUserSettings(UserSettingKeys.syncSelfMessageList);
      if (!setting?.msgIdArr) {
        setting = { msgIdArr: [] };
      }
      setting.msgIdArr.push({});
      index = setting.msgIdArr.length - 1;
      // param.start_msg_id = (index > 0 && setting.msgIdArr[index - 1]?.maxMsgId) || 1;
    }
    const obj = setting.msgIdArr[index];
    const res = await ImService.getMsgList(param);
    await this.saveOfflineMessageList(res, false, true);
    const length = res.data?.length;
    if (length) {
      obj.minMsgId = res.data[length - 1].msg_id;
      if (page === 1) {
        obj.maxMsgId = Math.max(res.data[0].msg_id, obj.maxMsgId || 0);
      }
      param.end_msg_id = obj.minMsgId;

      if (length === param.limit) {
        await Session.setUserSettings(UserSettingKeys.syncSelfMessageList, setting);
        return this.getSelfMessageList(param, page + 1, setting, index);
      }
    }
    if (index > 0 || !obj.maxMsgId) {
      setting.msgIdArr.splice(index, 1);
      index = setting.msgIdArr.length - 1;
      if (obj.maxMsgId) {
        setting.msgIdArr[index].maxMsgId = obj.maxMsgId;
      }
    }
    if (param.start_msg_id === 1) {
      setting.noMoreBefore = true;
    }
    await Session.setUserSettings(UserSettingKeys.syncSelfMessageList, setting);
    if (setting.noMoreBefore && index < 1) return;
    param.start_msg_id = (index > 0 && setting.msgIdArr[index - 1]?.maxMsgId) || 1;
    param.end_msg_id = setting.msgIdArr[index].minMsgId;
    return this.getSelfMessageList(param, 1, setting, index);
  };

  /**
   * 获取私聊离线消息列表
   */
  getOfflineMessageList = async () => {
    return this._getOfflineMessageList(false);
  };

  /**
   * 获取群离线消息列表
   */
  getGroupOfflineMessageList = async () => {
    return this._getOfflineMessageList(true);
  };

  /**
   * 获取群离线消息列表
   */
  _getOfflineMessageList = async (isGroup) => {
    const param = { limit: 500, msg_id: 0 };
    while (true) {
      const res = await ImService.getOfflineMsg(param, isGroup);
      console.debug('ImAction _getOfflineMessageList 1', isGroup);
      const lastMsgId = await this.saveOfflineMessageList(res, isGroup);
      console.debug('ImAction _getOfflineMessageList 2', isGroup, lastMsgId);
      await this.markReadMessage(lastMsgId, isGroup);
      if (res.data?.length !== param.limit) {
        break;
      }
      console.debug('ImAction _getOfflineMessageList again');
    }
  };

  /**
   * 保存离线消息列表
   */
  saveOfflineMessageList = async (res, isGroup, isDesc) => {
    if (!res.data?.length) return null;
    const { imId } = userStore;
    let lastMeetingCallMsg, lastSingleCallMsg;
    const sessionMsgMap = new Map();
    const withdrawMap = new Map();
    const noticeTypeMap = new Map();
    let data = res.data.filter((item) => {
      if (chatMessageUtil.existMsgId(item) || chatMessageUtil.isSkipMsg(item, isGroup)) {
        return false;
      }
      if (item.msg.type === constant.messageType.notice) {
        if (item.msg.data.notice_type) {
          CollectionUtil.mapArrayAdd(noticeTypeMap, item.msg.data.notice_type, item);
        }
        return false;
      }
      if (item.msg.type === constant.messageType.withdraw) {
        withdrawMap.set(item.msg.msgId, item);
        return false;
      }
      if (
        item.msg.type === constant.messageType.audioCall ||
        item.msg.type === constant.messageType.videoCall
      ) {
        //单聊音视频的离线消息
        lastSingleCallMsg = item;
        return false;
      }
      if (item.msg.type === constant.messageType.meetingCall) {
        //会议类型的离线消息
        lastMeetingCallMsg = item;
        return false;
      }

      if (!item.sessionId) {
        item.sessionId =
          item.group_id || (item.sender_id === imId ? item.receiver_id : item.sender_id);
      }
      CollectionUtil.mapArrayAdd(sessionMsgMap, item.sessionId, item);
      return true;
    });
    this.onSaveOfflineNoticeByMap(noticeTypeMap, isGroup);

    // 查找过滤本地已保存的消息
    let msgIdArr = [];
    if (sessionMsgMap.size) {
      const localMsgIdRes = await messageDao.queryMsgIds(sessionMsgMap);
      localMsgIdRes.forEach((r) => {
        // 查询失败的
        if (r.reason) {
          console.warn('MessageReadUtil queryMsgIds 查询失败', r.reason);
          return;
        }
        console.log('MessageReadUtil queryMsgIds', r.value);
        msgIdArr = msgIdArr.concat(r.value);
      });
      console.log('ImAction OfflineMessageList 1', msgIdArr.length);
      if (msgIdArr.length) {
        const msgIdSet = new Set(msgIdArr);
        data = data.filter((item) => {
          if (msgIdSet.has(`${item.sessionId}_${item.msg_id}`)) {
            console.log('ImAction OfflineMessageList 本地已存在', item.sessionId, item.msg_id);
            return false;
          }
          return true;
        });
      }
    }
    console.log('ImAction OfflineMessageList 1', data.length);

    if (data.length) {
      data = await this.isDisableSendByOffline(data, isGroup);
    }

    const map = new Map();
    for (const item of data) {
      console.debug('ImAction OfflineMessageList 2', item);
      await receiveMessageUtil.decryptMessage(item, isGroup);
      if (item.msg.content === '') {
        continue;
      }
      console.debug('ImAction OfflineMessageList 3', item.msg.content);
      const message = new ChatMessage();
      message.ownerId = imId;
      message.sessionId = item.sessionId;
      message.senderId = item.sender_id;
      message.msgId = item.msg_id;
      message.seq = item.msg_id;
      message.msgTime = item.msg_time;
      message.isSelfSend = item.sender_id === imId ? 1 : 0;
      message.isGroup = isGroup ? 1 : 0;
      if (message.isSelfSend) {
        message.sendState = constant.messageSendState.successful;
        message.isRead = 1;
        message.isArrived = 1;
      }

      chatMessageUtil.handleMessage(item.msg, message);
      chatMessageUtil.checkRefMessage(message);

      console.debug('ImAction OfflineMessageList 4', message);
      CollectionUtil.mapArrayAdd(map, message.sessionId, message);
    }
    if (!callAction.handleOfflineMessage(lastSingleCallMsg)) {
      meetingAction.handleOfflineMessage(lastMeetingCallMsg);
    }
    console.debug('ImAction OfflineMessageList 更新会话', map.size);

    for (const [sessionId, messages] of map) {
      await this.mergeWithdrawMsg(withdrawMap, messages);
      const list = await messageDao.batchSaveMessage(messages[0].ownerId, sessionId, messages);
      // for (let index = 0; index < messages.length; index++) {
      //   await messageDao.addMessage(messages[index]);
      // }
      await chatSessionDao.saveSessionByMsg(
        messages[isDesc ? 0 : messages.length - 1],
        list.length
      );
      global.emitter.emit(constant.event.receiveMessage, { message: messages, sessionId });
      sendMessageUtil.sendMsgReadByCheck({
        messages: list,
        session: { sessionId, isGroup: messages[0].isGroup },
        notice_type: constant.messageNoticeType.messageArrived,
      });
    }
    await this.saveWithdrawMsg(withdrawMap.values(), isGroup);
    return res.data[isDesc ? 0 : res.data.length - 1].msg_id;
  };

  mergeWithdrawMsg = async (withdrawMap, messages) => {
    if (!withdrawMap.size) return;
    let withdraw;
    for (let message of messages) {
      withdraw = withdrawMap.get(message.msgId);
      if (!withdraw) continue;
      await this.updateMsgByWithdraw(message);
      withdrawMap.delete(message.msgId);
      if (!withdrawMap.size) break;
    }
  };

  updateMsgByWithdraw = async (message) => {
    message.type = constant.messageType.tip;
    if (message.isGroup) {
      const groupMemberId = message.senderId;
      const groupMember = await this.getGroupMember({
        groupId: message.sessionId,
        imId: groupMemberId,
      });
      message.content = `${groupMember?.memo || groupMember?.nickname || groupMemberId}
      ${I18n.t('page_chat_withdraw_group')}`;
    } else {
      message.content = I18n.t(
        message.isSelfSend ? 'page_chat_withdraw_you' : 'page_chat_withdraw_p2p'
      );
    }
  };

  saveWithdrawMsg = async (withdraws) => {
    for (let data of withdraws) {
      await this.onWithdrawMsg(data);
    }
  };

  onWithdrawMsg = async ({ group_id, sender_id, receiver_id, msg: { msgId } }) => {
    const { imId } = userStore;
    const sessionId = group_id || (sender_id === imId ? receiver_id : sender_id);
    const message = await messageDao.getMessageById({ ownerId: imId, sessionId, msgId });
    if (!message) return;
    await this.updateMsgByWithdraw(message);
    await chatSessionDao.updateMessageWithSession(message, false);
    global.emitter.emit(constant.event.receiveMessage, { message });
  };

  /**
   * 批量处理消息
   * @param map {Map}
   * @param isGroup
   */
  onSaveOfflineNoticeByMap = async (map, isGroup) => {
    map.forEach((arr, key) => {
      this.onSaveOfflineNotice(key, arr, isGroup);
    });
  };

  onSaveOfflineNotice = async (noticeType, arr, isGroup) => {
    switch (noticeType) {
      case constant.messageNoticeType.addContact:
      case constant.messageNoticeType.agreeContact:
      case constant.messageNoticeType.AddedContact:
        // TODO 需支持批量处理
        arr.forEach((item) => this.onSaveAddContactNotice(item, true));
        break;
      case constant.messageNoticeType.groupInvite:
      case constant.messageNoticeType.groupRemove:
        // TODO 需支持批量处理
        arr.forEach((item) => this.onGroupInvite(item, true));
        break;
      case constant.messageNoticeType.messageRead:
      case constant.messageNoticeType.messageArrived:
        messageReadUtil.onMessageRead(arr);
        break;
    }
  };

  onSaveAddContactNotice = async (data) => {
    try {
      // {"sender_id":10293,"receiver_id":10292,"msg":{"data":{"add_by":"account","im_id":10293,"message":"Hi 我是R10293，想和你交个朋友","notice_type":"AddContact","req_id":"1948f0e6be70bb3c10293*************","rim_id":10292,"timestamp":*************},"type":"notice"},"msg_id":****************,"msg_time":*************,"send_self":0}
      // if (data.sender_id === userStore.imId) return;
      const receiveData = data.msg.data;
      console.log('imAction onSaveAddContactNotice', data);
      const { imId: ownerId } = userStore;
      const isSelfSend = ownerId === data.sender_id;
      const isAgreeContact = receiveData.notice_type === constant.messageNoticeType.agreeContact;
      const imId = isSelfSend ? data.receiver_id : data.sender_id;
      const { user: reqUser } = await chatAction.getFriend(imId, false, true);
      // 仅保存更新对方发送的好友申请
      let contactModel = await contactDao.findContact({ imId });
      console.log('imAction onSaveAddContactNotice contactModel', contactModel);
      const isAdd = !contactModel;
      if (isAdd) {
        contactModel = new ContactModel();
        contactModel.isSelfReq = isSelfSend ? 0 : 1;
        contactModel.imId = imId;
        contactModel.ownerId = ownerId;
        contactModel.message = receiveData.message;
        contactModel.notifyId = 0;
        contactModel.addBy = receiveData.add_by || 'account';
        contactModel.addFrom = 'phone';
      } else {
        contactModel.message = receiveData.message || contactModel.message;
        contactModel.addBy = receiveData.add_by || contactModel.addBy;
        if (!isAgreeContact) {
          contactModel.isSelfReq = isSelfSend ? 0 : 1;
        }
      }
      if (reqUser) {
        contactModel.nickname = reqUser.nickname || reqUser.account_alias || '';
        contactModel.avatar = reqUser.avatar || '';
      }
      contactModel.requestAt = receiveData.timestamp || new Date().getTime();
      contactModel.reqId = receiveData.req_id;
      contactModel.isDeleteBy =
        receiveData.notice_type === constant.messageNoticeType.deleteContacts ? 1 : 0;
      if (reqUser?.relation_type === 1) {
        contactModel.status = 1; // 0等待验证 1已同意 2已拒绝
        contactModel.isBlacked = 0;
        contactModel.isRead = 1;
      } else if (isAgreeContact) {
        // 如果非好友，且收到同意好友消息时跳过，这条消息应该是滞后的离线消息列表中的
        return;
      } else {
        contactModel.status = contactModel.isDeleteBy ? 2 : 0;
        contactModel.isBlacked = 0;
        contactModel.isRead = isSelfSend ? 1 : 0;
      }
      if (isAdd) {
        await contactDao.insertNewContact(contactModel);
      } else {
        await contactDao.updateContact(contactModel);
      }

      global.emitter.emit(constant.event.addContact, { imId });
      let msgId = parseFloat(data.msg_id);
      let msgTime = parseFloat(data.msg_time);
      const reqInfo = await contactDao.findContact({ imId: userStore.imId });
      if (isAgreeContact && reqInfo?.message) {
        await this.addMessage({
          isGroup: 0,
          sessionId: imId,
          senderId: userStore.imId,
          content: reqInfo.message,
          msgId,
          msgTime,
          isRead: 0,
          type: constant.messageType.newFriend,
        });
        msgId++;
        msgTime++;
      }
      if (isAgreeContact && !isSelfSend) {
        await this.addMessage({
          isGroup: 0,
          sessionId: imId,
          senderId: imId,
          content: I18n.t('page_message_tips_pass_friend'),
          msgId,
          msgTime,
          isRead: 0,
          type: constant.messageType.newFriend,
        });
        msgId++;
        msgTime++;
        // const message = await this.addTipMessage({
        //   isGroup: 0,
        //   sessionId: imId,
        //   senderId: imId,
        //   content: I18n.t('page_chat_tips_add_agreed'),
        //   msgId,
        //   msgTime,
        //   isRead: 0,
        // });
        // global.emitter.emit(constant.event.receiveMessage, { message });
      }
      if (receiveData.notice_type === constant.messageNoticeType.AddedContact && !isSelfSend) {
        await this.addMessage({
          isGroup: 0,
          sessionId: imId,
          senderId: imId,
          content: receiveData.message,
          msgId,
          msgTime,
          isRead: 0,
          type: constant.messageType.newFriend,
        });
        msgId++;
        msgTime++;
      }
    } catch (e) {
      console.warn('imAction onSaveAddContactNotice', e);
      return Promise.reject(e);
    }
  };

  agreeAndAddContact = async (item) => {
    const imId = item.imId || item.im_id;
    item.im_id = imId;
    item.imId = imId;
    await chatAction.addContact(imId);
    await contactDao.updateContact(item);
    await sendMessageUtil.sendContactSocket({
      noticeType: constant.messageNoticeType.agreeContact,
      contact: {
        ...item,
        status: 1,
        isSelfReq: 1,
        im_id: imId,
      },
    });
    global.emitter.emit(constant.event.addContact, { imId });
    let msgId = new Date().getTime();
    const reqInfo = await contactDao.findContact({ imId });
    console.log('imAction agreeAndAddContact', reqInfo, item);
    if (reqInfo?.message) {
      await this.addMessage({
        isGroup: 0,
        sessionId: imId,
        senderId: imId,
        content: reqInfo.message,
        msgId,
        msgTime: msgId,
        isRead: 0,
        type: constant.messageType.newFriend,
      });
    }
    msgId++;
    await this.addMessage({
      isGroup: 0,
      sessionId: imId,
      senderId: userStore.imId,
      content: I18n.t('page_message_tips_pass_friend'),
      msgId,
      msgTime: msgId,
      isRead: 0,
      type: constant.messageType.newFriend,
    });
    msgId++;
    // await this.addTipMessage({
    //   isGroup: 0,
    //   sessionId: imId,
    //   senderId: imId,
    //   content: I18n.t('page_chat_tips_add_agree'),
    //   msgId,
    //   msgTime: msgId,
    //   isRead: 1,
    // });
  };

  addTipMessage = async (chatMessage) => {
    chatMessage.type = constant.messageType.tip;
    return this.addMessage(chatMessage);
  };

  addMessage = async ({ isGroup, senderId, msgId, msgTime, isRead, ...rest }) => {
    const chatMessage = new ChatMessage();
    chatMessage.ownerId = userStore.imId;
    // chatMessage.sessionId = sessionId;
    chatMessage.isGroup = isGroup ? 1 : 0;
    // chatMessage.content = content;
    chatMessage.msgTime = msgTime || new Date().getTime();
    chatMessage.seq = msgId;
    chatMessage.msgId = msgId;
    chatMessage.senderId = senderId;
    chatMessage.isSelfSend = senderId === chatMessage.ownerId ? 1 : 0;
    if (typeof isRead === 'undefined') {
      chatMessage.isRead = chatMessage.isSelfSend ? 1 : 0;
    } else {
      chatMessage.isRead = isRead ? 1 : 0;
    }
    chatMessage.sendState = constant.messageSendState.successful;
    Object.assign(chatMessage, rest);
    const exist = await messageDao.existById(chatMessage);
    chatMessage.exist = exist;
    if (!exist) {
      await chatSessionDao.updateMessageWithSession(chatMessage, true);
    }
    global.emitter.emit(constant.event.receiveMessage, { message: chatMessage });
    return chatMessage;
  };

  /**
   * 清空离线消息列表
   */
  markReadMessage = async (msgId, isGroup) => {
    if (!msgId) return null;
    return ImService.getOfflineMsg({ limit: 1, msg_id: msgId }, isGroup);
  };

  /**
   * 清空离线通知列表
   */
  markReadNotice = async (noticeId, isGroup) => {
    if (!noticeId) return null;
    return ImService.getOfflineNotice({ limit: 1, notice_id: noticeId }, isGroup);
  };

  /**
   * 获取群列表
   */
  getGroupList = async (isRefreshMembers) => {
    const { imId } = userStore;
    const [res, localGroups] = await Promise.all([
      ImService.getGroupList(),
      chatGroupDao.getGroups({ ownerId: imId }),
    ]);
    // console.log('getGroupList getGroupList', res);
    const localGroupMap = new Map();
    localGroups.forEach((item) => localGroupMap.set(item.groupId, item));
    const result = {
      insert: [],
      update: [],
    };
    (res?.groups || []).forEach((item) => {
      let chatGroup = localGroupMap.get(item.group_id);
      if (chatGroup) {
        result.update.push(chatGroup);
        localGroupMap.delete(item.group_id);
      } else {
        chatGroup = new ChatGroup();
        chatGroup.ownerId = imId;
        chatGroup.groupId = item.group_id;
        result.insert.push(chatGroup);
      }
      chatGroup.isMemberChange = chatGroup.memberHash !== item.member_hash;
      chatGroup.title = item.name;
      chatGroup.avatar = chatMessageUtil.handleAvatar(item);
      chatGroup.count = item.count;
      chatGroup.isVip = item.is_vip;
      chatGroup.memberHash = item.member_hash;
    });
    result.delete = [...localGroupMap.values()];
    await chatGroupDao.saveGroups(result);
    const groupArr = result.insert.concat(result.update);
    if (isRefreshMembers) {
      // 刷新群成员列表，每次最多同时请求5个群的
      let tasks = [];
      for (const chatGroup of groupArr) {
        if (chatGroup.isMemberChange) {
          tasks.push(this.getGroupMemberList(chatGroup.groupId));
        }
        if (tasks.length > 4) {
          await promiseUtil.allSettled(tasks);
          tasks = [];
        }
      }
      if (tasks.length) {
        await promiseUtil.allSettled(tasks);
      }
    }
    return groupArr;
  };

  getGroupInfo = async (groupId, { isLocal, syncMember, forceRefreshMember } = {}) => {
    const { imId: ownerId } = userStore;
    let chatGroup, remoteGroup;
    if (isLocal) {
      chatGroup = await chatGroupDao.getGroup({ ownerId, groupId });
      if (chatGroup) return chatGroup;
      remoteGroup = await ImService.getGroupInfo(groupId);
    } else {
      const res = await Promise.all([
        ImService.getGroupInfo(groupId),
        chatGroupDao.getGroup({ ownerId, groupId }),
      ]);
      chatGroup = res[1];
      remoteGroup = res[0];
    }
    let isAdd = !chatGroup;
    if (isAdd) {
      chatGroup = new ChatGroup();
      chatGroup.ownerId = ownerId;
      chatGroup.groupId = groupId;
    }
    chatGroup.groupOwnerId = remoteGroup.owner_id;
    chatGroup.creatorId = remoteGroup.creator_id;
    chatGroup.groupType = remoteGroup.group_type;
    chatGroup.title = remoteGroup.name;
    chatGroup.avatar = chatMessageUtil.handleAvatar(remoteGroup);
    chatGroup.isMemberChange = chatGroup.memberHash !== remoteGroup.member_hash;
    chatGroup.isRemote = true; // 是否请求了服务器
    chatGroup.memberHash = remoteGroup.member_hash;
    chatGroup.count = remoteGroup.count;
    chatGroup.isVip = remoteGroup.is_vip;
    // chatGroup.canInvite = remoteGroup.setting.invite;
    // chatGroup.canAddFriend = remoteGroup.setting.add_in_group;
    chatGroup.noSpeaking = remoteGroup.setting.no_speaking;
    chatGroup.memberVisible = remoteGroup.setting.member_visible;
    chatGroup.enableMedia = remoteGroup.setting.enable_media;
    chatGroup.limitTime = remoteGroup.setting.limit_time;
    chatGroup.isPrivate = remoteGroup.setting.public_group === 0 ? 1 : 0;
    // chatGroup.updateTime = remoteGroup.update_at * 1000;
    if (isAdd) {
      await chatGroupDao.addGroup(chatGroup);
    } else {
      await chatGroupDao.updateGroup(chatGroup);
    }
    if (chatGroup.isMemberChange || forceRefreshMember) {
      if (syncMember) {
        const { groupMembers } = await this.getGroupMemberList(groupId);
        chatGroup.groupMembers = groupMembers;
      } else {
        this.getGroupMemberList(groupId);
      }
    }
    return chatGroup;
  };

  /**
   * 获取指定群成员
   * @param param {{groupId, imId}}
   * @return {Promise<void>}
   */
  getGroupMember = async (param) => {
    const groupMember = await groupMemberDao.getGroupMember(param);
    if (groupMember) return groupMember;
    const { groupMembers } = await this.getGroupMemberList(param.groupId, false, true);
    return groupMembers.find((item) => item.imId === param.imId);
  };

  /**
   * 获取群成员列表
   * @param groupId 群ID
   * @param isLocal 是否优先获取本地的，本地不存在的情况下再获取服务器的
   * @param includeDeleted 是否包含已删除的
   * @return {Promise<{groupMembers: GroupMember[], hasRemote: boolean}>}
   */
  getGroupMemberList = async (groupId, isLocal, includeDeleted) => {
    const { imId } = userStore;
    let localGroupMembers, remoteGroupMembers, groupRoles, groupMemberSettings;
    if (isLocal) {
      localGroupMembers = await groupMemberDao.getGroupMembers({
        ownerId: imId,
        groupId,
        isAll: true,
      });
      if (!localGroupMembers.length) {
        const [remote, roles, memberSetting] = await Promise.all([
          ImService.getGroupMembers(groupId),
          ImService.getGroupRoles(groupId),
          ImService.getGroupMemberSettingList(groupId, imId),
        ]);
        remoteGroupMembers = remote?.members || [];
        groupRoles = roles;
        groupMemberSettings = memberSetting?.settings || [];
      }
    } else {
      const [remote, local, roles, memberSetting] = await Promise.all([
        ImService.getGroupMembers(groupId),
        groupMemberDao.getGroupMembers({ ownerId: imId, groupId, isAll: true }),
        ImService.getGroupRoles(groupId),
        ImService.getGroupMemberSettingList(groupId, imId),
      ]);
      localGroupMembers = local;
      remoteGroupMembers = remote?.members || [];
      groupRoles = roles;
      groupMemberSettings = memberSetting?.settings || [];
    }
    if (!remoteGroupMembers) {
      return {
        groupMembers: includeDeleted
          ? localGroupMembers
          : localGroupMembers.filter((item) => !item.isDelete),
        hasRemote: false,
      };
    }
    const localGroupMemberMap = new Map();
    localGroupMembers.forEach((item) => localGroupMemberMap.set(item.imId, item));
    const roleMap = new Map();
    groupRoles.forEach((item) => roleMap.set(item.im_id, item));
    const settingMap = new Map();
    groupMemberSettings.forEach((item) => settingMap.set(item.im_id, item));
    const result = {
      insert: [],
      update: [],
      delete: [],
    };
    remoteGroupMembers.forEach((item) => {
      let groupMember = localGroupMemberMap.get(item.im_id);
      if (groupMember) {
        result.update.push(groupMember);
        localGroupMemberMap.delete(item.im_id);
      } else {
        groupMember = new GroupMember();
        groupMember.imId = item.im_id;
        result.insert.push(groupMember);
      }
      groupMember.noSpeaking = settingMap.get(item.im_id)?.no_speaking ? 1 : 0;
      groupMember.nickname = item.nickname || item.account_alias;
      groupMember.memo = item.group_nickname || '';
      groupMember.joinAt = item.join_at * 1000;
      groupMember.avatar = chatMessageUtil.handleAvatar(item);
      groupMember.role = roleMap.get(item.im_id)?.role || constant.groupMemberRole.member;
      groupMember.user_type = item.user_type;
    });
    result.delete = [...localGroupMemberMap.values()];
    await groupMemberDao.saveGroupMembers({ data: result, ownerId: imId, groupId });
    if (includeDeleted) {
      return {
        groupMembers: [...result.insert, ...result.update, ...result.delete],
        hasRemote: true,
      };
    }
    return { groupMembers: [...result.insert, ...result.update], hasRemote: true };
  };

  @action
  getCurrentMessageMemberMap = async (session, isLocal) => {
    const map = new Map();
    if (session.isGroup) {
      const [{ groupMembers }, allFriends] = await Promise.all([
        this.getGroupMemberList(session.sessionId, isLocal, true),
        chatAction.getFriends(isLocal),
      ]);
      groupMembers.forEach((item) => {
        const friend = allFriends.find((x) => x.imId === item.imId);
        if (friend) {
          item.friendMemo = friend.memo;
        }
        item.nameColor = '#' + Math.floor(Math.random() * 0xffffff).toString(16);
        map.set(item.imId, item);
      });
    } else if (session.sessionId === session.ownerId) {
      map.set(session.sessionId, {
        imId: session.sessionId,
        avatar: session.avatar,
        title: session.title,
        nickname: session.title,
        userType: session.userType,
      });
    } else {
      const { friend } = await chatAction.getFriend(session.sessionId, isLocal, true);
      if (friend && !session.isManage) {
        // console.log('getCurrentMessageMemberMap', isLocal, friend, session);
        session.avatar = friend.avatar;
        session.title = friend.memo || friend.nickname;
        session.nickname = session.title;
        // 处理公司显示
        const extra = friend.extra && sentryUtil.parse(friend.extra);
        if (extra?.contact?.name) {
          friend.company = extra.company || friend.nickname;
          session.company = friend.company;
          session.friendExtra = extra;
        }
      }

      map.set(session.sessionId, {
        ...(friend || {}),
        imId: session.sessionId,
        avatar: session.avatar,
        title: session.title,
        nickname: session.title,
      });
    }
    console.log(map);
    return map;
  };

  isDisableSendByOffline = async (data, isGroup) => {
    const { isManage, imId: ownerId } = userStore;
    const groupIdMap = new Map();
    const friendIdSet = new Set();
    const remoteFriendIdSet = new Set();
    data.forEach((item) => {
      if (isGroup) {
        CollectionUtil.mapSetAdd(groupIdMap, item.sessionId, item.sender_id);
      } else if (item.sessionId !== ownerId) {
        // 自己是管理员时，可接收任何人的消息
        // 自己发给自己的不用查
        friendIdSet.add(item.sessionId);
        // 如果是管理员发的公告，需要查询服务器验证身份
        // 以后如果某些消息类型允许陌生人发送，如转账，也需要查询服务器验证身份
        // if (item.msg.isBroadCast || isManage) {
        // }
        remoteFriendIdSet.add(item.sessionId);
      }
    });
    const sqlArr = [];
    if (groupIdMap.size) {
      sqlArr.push({
        sql: `SELECT 'group' AS dType,groupId,noSpeaking FROM t_group WHERE groupId IN (${[
          ...groupIdMap.keys(),
        ].join(',')})`,
        values: [],
      });
      groupIdMap.forEach((set, groupId) => {
        sqlArr.push({
          sql: `SELECT 'groupMember' AS dType, ${groupId} AS groupId,imId,role,isDelete,noSpeaking FROM ${getGroupMemberTableName(
            ownerId,
            groupId
          )} WHERE imId IN (${[...set].join(',')})`,
          values: [],
        });
      });
    }
    if (friendIdSet.size) {
      sqlArr.push({
        sql: `SELECT *,'friend' AS dType FROM t_friend WHERE imId IN (${[...friendIdSet].join(
          ','
        )})`,
        values: [],
      });
    }
    if (!sqlArr.length) return data;

    function handleResult(res) {
      return databaseUtil.getArray(res);
    }
    const batchRes = await sqliteService.batchQuery(sqlArr, handleResult, {
      createTableSqlFun: getCreateGroupMemberTableSQL,
      tablePrefix: 't_group_member_',
    });
    const groupMap = new Map();
    const groupMemberMap = new Map();
    const friendMap = new Map();
    batchRes.forEach((res) => {
      // 查询失败的
      if (res.reason) {
        console.warn('imAction info 查询失败', res.reason);
        return;
      }
      console.log('imAction info', res.value);
      res.value.forEach((it) => {
        if (!isGroup) {
          friendMap.set(it.imId, it);
        } else if (it.dType === 'group') {
          groupMap.set(it.groupId, it);
        } else if (it.dType === 'groupMember') {
          groupMemberMap.set(`${it.groupId}_${it.imId}`, it);
        }
      });
    });

    // 查询本地不存在的用户数据
    if (remoteFriendIdSet.size) {
      const tasks = [];
      for (let imId of remoteFriendIdSet) {
        if (!friendMap.has(imId)) {
          tasks.push(chatAction.getFriend(imId, false, true));
        }
      }
      if (tasks.length) {
        const res = await promiseUtil.allSettled(tasks);
        res.forEach((it) => {
          if (it.value?.friend) {
            friendMap.set(it.value.friend.imId, it.value.friend);
          } else {
            console.warn('离线消息 获取服务端用户数据失败', it.value, it.reason);
          }
        });
      }
    }

    function getGroupFun(sessionId) {
      return groupMap.get(sessionId);
    }
    function groupMemberFun(sessionId, senderId) {
      return groupMemberMap.get(`${sessionId}_${senderId}`);
    }
    function getFriendFun(sessionId) {
      return friendMap.get(sessionId);
    }
    return data.filter((item) => {
      return !this.checkInvalidMsg(
        isGroup,
        item.sessionId,
        item.sender_id,
        getGroupFun,
        groupMemberFun,
        getFriendFun,
        item
      );
    });
  };

  /**
   * 检查过滤非法消息
   */
  checkInvalidMsg = (
    isGroup,
    sessionId,
    senderId,
    getGroupFun,
    groupMemberFun,
    getFriendFun,
    item
  ) => {
    if (isGroup) {
      const group = getGroupFun(sessionId);
      if (!group) {
        console.warn('无效的离线消息：群为null', item);
        return constant.invalidMsgType.notGroup;
      }
      const groupMember = groupMemberFun(sessionId, senderId);
      if (!groupMember || groupMember.isDelete) {
        console.warn('无效的离线消息：群成员为null', groupMember?.isDelete, item);
        return constant.invalidMsgType.notGroup;
      }
      // 普通群成员被禁言后不能发消息
      if (groupMember.role === 'member') {
        if (group.noSpeaking) {
          console.warn('无效的离线消息：群禁言', item);
          return constant.invalidMsgType.noSpeakingGroup;
        }
        if (groupMember.noSpeaking) {
          console.warn('无效的离线消息：群成员禁言', item);
          return constant.invalidMsgType.noSpeakingGroupMember;
        }
      }
      return null;
    }
    const { isManage, imId: ownerId } = userStore;
    // 自己发送的消息不过滤
    if (ownerId === sessionId) return null;
    const friend = getFriendFun(sessionId);
    if (!friend) {
      console.warn('无效的离线消息：好友为null', item);
      return constant.invalidMsgType.notFriend;
    }
    // 自己是管理员，消息不过滤
    if (isManage) return null;
    // 管理员发送的消息 不过滤
    if (friend.userType === constant.userType.manage) return null;
    // 0:未拉黑  1:已拉黑好友  2:好友已拉黑你  3:相互拉黑
    if (
      friend.isBlacked === 3 ||
      (senderId === ownerId ? friend.isBlacked === 2 : friend.isBlacked === 1)
    ) {
      console.warn('无效的离线消息：好友黑名单', friend.isBlacked, item);
      return constant.invalidMsgType.blackedFriend;
    }
    // 被删除好友、非好友
    /*if (friend.beDeleted || friend.isHide) {
      // 判断消息类型，可接收非好友的转账消息，发送给非好友转账消息时，发送者不保存改消息
      const type = item.type || item.msg?.type;
      if (type === constant.messageType.transfer) {
        if (senderId !== ownerId) {
          return null;
        }
        if (item.isSend) {
          return constant.invalidMsgType.onlySendNotSave;
        }
      }

      console.warn('无效的离线消息：非好友', friend.beDeleted, friend.isHide, item);
      return constant.invalidMsgType.notFriend;
    }*/
    return null;
  };

  /**
   * 是否禁止发送消息，如：已解除好友关系，群解散或被踢出群
   */
  isDisableSend = async ({ sessionId, isGroup, imId = userStore.imId, ...rest }) => {
    const { isManage, imId: ownerId } = userStore;
    if (isGroup) {
      let group, groupMember;
      // 如果是当前会话，从会话中获取数据
      if (chatStore.currentSessionInfo?.groupInfo?.groupId === sessionId) {
        group = chatStore.currentSessionInfo.groupInfo;
        groupMember = chatStore.currentSessionInfo.memberMap?.get(imId);
      }
      if (!group) {
        // 优先从本地获取，没有的情况下从服务器获取
        group = await this.getGroupInfo(sessionId, { isLocal: true, syncMember: true });
      }
      if (!groupMember) {
        groupMember = await groupMemberDao.getGroupMember({ groupId: sessionId, imId });
        if (!groupMember) {
          // 优先从本地获取，没有的情况下从服务器获取
          const res = await this.getGroupMemberList(sessionId, true);
          groupMember = res.groupMembers.find((it) => it.imId === imId);
        }
      }
      const invalidMsgType = this.checkInvalidMsg(
        true,
        sessionId,
        imId,
        () => group,
        () => groupMember,
        null,
        rest
      );

      return { disableSend: invalidMsgType, invalidMsgType };
    }
    if (sessionId === ownerId) {
      return { disableSend: false };
    }
    // 如果是当前会话，从会话中获取数据
    let friend = chatAction.getCurrentSessionMember(sessionId, sessionId, isGroup);
    if (!friend) {
      // 优先从本地获取，没有的情况下从服务器获取
      const res = await chatAction.getFriend(sessionId, true, true);
      friend = res.friend;
    }
    // 管理员可以接收任何人的消息，这里也要等查询保存了用户信息
    if (isManage) {
      return { disableSend: !friend };
    }
    const invalidMsgType = this.checkInvalidMsg(
      false,
      sessionId,
      imId,
      null,
      null,
      () => friend,
      rest
    );
    console.log('imAction isDisableSend', invalidMsgType, friend);
    return { disableSend: invalidMsgType, invalidMsgType, infoData: friend };
  };

  /**
   * 创建群聊
   * @param param
   * @return {Promise<Map<any, any>>}
   */
  createGroupChat = async ({ members, name }) => {
    const { imId } = userStore;
    const map = new Map();
    map.set(imId, { imId });
    members.forEach((item) => map.set(item.imId || item.im_id, item));
    const res = await ImService.createGroup({
      name,
      im_ids: [...map.keys()],
    });
    console.log('createGroupChat createGroup', res);
    // 邀请加入的人可能被服务端过滤掉，如对方加你到黑名单，你不能邀请别人入群
    const chatGroup = await this.getGroupInfo(res.data.group_id, { syncMember: true });
    let chatMessage = new ChatMessage();
    chatMessage.ownerId = chatGroup.ownerId;
    chatMessage.sessionId = chatGroup.groupId;
    chatMessage.isGroup = 1;
    chatMessage.content = I18n.t('page_chat_tips_create_group');
    chatMessage.msgTime = res.data.create_at * 1000;
    chatMessage.seq = chatMessage.msgTime;
    chatMessage.msgId = chatMessage.msgTime;
    chatMessage.type = constant.messageType.tip;
    chatMessage.senderId = chatGroup.ownerId;
    chatMessage.isSelfSend = 1;
    chatMessage.isRead = 1;
    chatMessage.sendState = constant.messageSendState.successful;
    await chatSessionDao.updateMessageWithSession(chatMessage, true);
    const imIds = [];
    const targetNames = [];
    chatGroup.groupMembers.forEach((item) => {
      imIds.push(item.imId);
      if (item.imId !== imId) {
        targetNames.push(item.memo || item.nickname);
      }
      map.delete(item.imId);
    });
    if (targetNames.length) {
      await sendMessageUtil.sendGroupInvite({
        groupId: chatGroup.groupId,
        imIds,
        targetNames: targetNames.join('、'),
      });
    }
    if (map.size) {
      chatMessage = { ...chatMessage };
      chatMessage.content = I18n.t('msg_reject_join_group', {
        names: [...map.values()].map((it) => it.memo || it.nickname).join('、'),
      });
      chatMessage.msgTime++;
      chatMessage.seq++;
      chatMessage.msgId = chatMessage.seq;
      await chatSessionDao.updateMessageWithSession(chatMessage, true);
    }
    return chatGroup;
  };

  /**
   * 收到群聊邀请
   * @param data {object} {"notice_time":1635732468594364,"notice":{"charter_member":10103,"group_id":1635732468510001,"member_ids":[10102,10103],"notify_type":"group_invite"},"notice_id":1635732468594310}
   * @return {Promise<Map<any, any>>}
   */
  @action
  onGroupInvite = async (data) => {
    try {
      const isMsg = !!data.msg;
      if (isMsg) {
        data.msg.data.notify_type = data.msg.data.notice_type;
        data = {
          notice: data.msg.data,
          notice_id: data.msg_id,
          notice_time: data.msg_time,
        };
      } else {
        data.notice_time = parseInt(data.notice_time / 1000, 10);
      }

      const { notice } = data;
      const isAddMember = notice.notify_type === constant.messageNoticeType.groupInvite;
      const { imId: ownerId } = userStore;
      if (
        !isAddMember &&
        notice.charter_member === ownerId &&
        notice.member_ids.length === 1 &&
        notice.member_ids[0] === ownerId
      ) {
        // 退出群聊
        return;
      }
      const { group_id: groupId } = notice;
      const [{ groupMembers }] = await Promise.all([
        this.getGroupMemberList(groupId, false, true),
        this.getGroupInfo(groupId),
      ]);
      global.emitter.emit(constant.event.groupMemberChange, { groupId, isLocal: true });
      const map = new Map();
      groupMembers.forEach((item) => map.set(item.imId, item));

      let invitor;
      if (notice.charter_member === ownerId) {
        invitor = I18n.t('page_group_tips_manage_you');
      } else {
        invitor = map.get(notice.charter_member)?.nickname || notice.charter_member.toString();
      }
      let includeMe = false;
      let targetNames = notice.member_ids
        .filter((id) => {
          if (id === notice.charter_member) return false;
          if (id === ownerId) {
            includeMe = true;
            return false;
          }
          return true;
        })
        .map((id) => map.get(id)?.nickname || id.toString())
        .join('、');
      let content;
      if (isAddMember) {
        if (includeMe) {
          if (targetNames) {
            content = I18n.t('page_group_tips_invite_you_other', {
              invitor,
              names: targetNames,
            });
          } else {
            content = I18n.t('page_group_tips_invite_you', { invitor });
          }
        } else {
          content = I18n.t('page_group_tips_invite_other', {
            invitor,
            names: targetNames,
          });
        }
      } else {
        if (includeMe) {
          if (targetNames) {
            content = I18n.t('page_group_tips_remove_you_other', {
              invitor,
              names: targetNames,
            });
          } else {
            content = I18n.t('page_group_tips_remove_you', { invitor });
          }
        } else if (targetNames) {
          content = I18n.t('page_group_tips_remove_other', {
            invitor,
            names: targetNames,
          });
        } else if (notice.charter_member === ownerId) {
          // 自己退出了群聊
          return;
        } else {
          content = I18n.t('page_group_tips_quit', { invitor });
        }
      }
      const chatMessage = await this.addTipMessage({
        isGroup: 1,
        sessionId: groupId,
        content,
        senderId: notice.charter_member,
        msgId: data.notice_id,
        msgTime: data.notice_time,
      });
      if (!chatMessage.exist) {
        global.emitter.emit(constant.event.receiveMessage, { message: chatMessage });
        global.emitter.emit(constant.event.chatSessionChange);
      }
    } catch (e) {
      console.warn('imAction onGroupInvite', e);
      return Promise.reject(e);
    }
  };

  /**
   * 收到群成员角色变更
   * @param data {object} {"notice_time":1636352146488203,"notice":{"data":{"group_id":1636352069510001,"im_ids":[10102,10104],"operator_id":10104,"role":"owner","update_at":1636352146},"notify_type":"GroupRole"},"notice_id":1636352146488210}
   */
  @action
  onGroupRole = async (data) => {
    try {
      const { notice } = data;
      const { group_id: groupId, im_ids, operator_id, role } = data.notice.data;
      const { imId: ownerId } = userStore;
      if (role === constant.groupMemberRole.owner && operator_id === ownerId) {
        // 自己转移群主，一般是退出群聊
        return;
      }
      const [{ groupMembers }, groupInfo] = await Promise.all([
        this.getGroupMemberList(groupId, false, false),
        this.getGroupInfo(groupId),
      ]);
      const groupMemberMap = new Map();
      groupMembers.forEach((item) => groupMemberMap.set(item.imId, item));
      const operatorName =
        operator_id === ownerId
          ? I18n.t('page_group_tips_manage_you')
          : groupMemberMap.get(operator_id)?.nickname || operator_id.toString();
      let content;
      if (role === constant.groupMemberRole.owner) {
        const groupOwnerId = im_ids.find((item) => item !== operator_id);
        if (groupOwnerId === ownerId) {
          content = I18n.t('page_group_tips_owner_you', { name: operatorName });
        } else {
          content = I18n.t('page_group_tips_owner_other', {
            name: operatorName,
            other: groupMemberMap.get(groupOwnerId)?.nickname,
          });
        }
      } else {
        let targetNames = im_ids.map((item) => {
          if (item === ownerId) {
            return I18n.t('page_group_tips_manage_you');
          }
          return groupMemberMap.get(item)?.nickname || item.toString();
        });
        if (role === constant.groupMemberRole.admin) {
          content = I18n.t('page_group_tips_magage_other', {
            name: operatorName,
            other: targetNames,
          });
        } else {
          content = I18n.t('page_group_tips_magage_cancel', {
            name: operatorName,
            other: targetNames,
          });
        }
      }

      const chatMessage = await this.addTipMessage({
        isGroup: 1,
        sessionId: groupId,
        content,
        senderId: notice.operator_id,
        msgId: data.notice_id,
        msgTime: parseInt(data.notice_time / 1000, 10),
      });
      global.emitter.emit(constant.event.receiveMessage, { message: chatMessage });
      global.emitter.emit(constant.event.chatSessionChange);
      global.emitter.emit(constant.event.groupRoleChange, { groupId });
      global.emitter.emit(constant.event.groupInfoChange, { groupId, groupInfo });
      global.emitter.emit(constant.event.groupMemberChange, { groupId, isLocal: true });
    } catch (e) {
      console.warn('imAction onGroupRole', e, data);
      return Promise.reject(e);
    }
  };

  deleteSession = async ({ ownerId = userStore.imId, sessionId, isGroup }) => {
    return Promise.all([
      chatSessionDao.deleteSession({ ownerId, sessionId, isGroup }),
      messageDao.clearMessageList({ ownerId, sessionId }),
    ]);
  };

  /**
   * 当前用户主动删除好友
   * @param imId
   * @return {Promise<void>}
   */
  deleteFriend = async ({ imId }) => {
    await Promise.all([
      friendDao.deleteFriend({ imId, isDelete: true }),
      this.deleteSession({ sessionId: imId, isGroup: 0 }),
      // contactDao.deleteContact({ imId }),
    ]);
    global.emitter.emit(constant.event.deleteContact, { imId });
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 退出群聊
   * @param groupId
   * @param isDeleted 是否已经不在该群了
   * @return {Promise<void>}
   */
  exitGroup = async (groupId, isDeleted) => {
    if (!isDeleted) {
      try {
        await ImService.exitGroup({ groupId, imId: userStore.imId });
      } catch (e) {
        if (e && e.code !== -801) {
          return Promise.reject(e);
        }
      }
    }
    await Promise.all([
      this.deleteSession({ sessionId: groupId, isGroup: 1 }),
      groupMemberDao.cleanGroupMember({ groupId }),
      chatGroupDao.deleteGroup({ groupId }),
    ]);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 删除好友通知
   * {"notice_time":1635931583079740,"notice":{"data":{"im_id":10102,"receive_id":10167,"send_id":10102,"timestamp":1635931583},"is_push":0,"notify_type":"DeleteContacts"},"notice_id":1635931583079710}
   * @return {Promise<void>}
   */
  onDeleteContactNotice = async (data) => {
    try {
      const { send_id, receive_id } = data.notice.data;
      const isSelfSend = userStore.imId === send_id;
      const friendId = isSelfSend ? receive_id : send_id;
      if (isSelfSend) {
        await this.deleteFriend({ imId: friendId });
      } else {
        await Promise.all([
          friendDao.deleteFriend({ imId: friendId }),
          // contactDao.deleteContact({ imId: friendId }),
        ]);
        global.emitter.emit(constant.event.deleteContact, { imId: friendId });
      }
    } catch (e) {
      console.warn('imAction onDeleteContactNotice', e);
      return Promise.reject(e);
    }
  };

  onNoSpeaking = async (data) => {
    const { group_id: groupId } = data.notice.data;
    await this.getGroupMemberList(groupId);
    global.emitter.emit(constant.event.groupMemberChange, { groupId, isLocal: true });
  };

  /**
   * 收到群信息修改-(群名称等)
   *{"event":"notice","data":{"notice_time":1636787835924074,"notice":{"data":{"group_id":1636783588410001,"update_at":1636787835},"notify_type":"ModifyGroup"},"notice_id":1636787835924010}}
   * @memberof ImAction
   */
  onGroupModify = async (data) => {
    const { group_id: groupId } = data.notice.data;
    const [_, groupInfo] = await Promise.all([
      this.getGroupMemberList(groupId, false, true),
      this.getGroupInfo(groupId),
    ]);
    global.emitter.emit(constant.event.groupInfoChange, { groupId, groupInfo });
  };

  /**
   * 收到群成员信息修改-(群昵称等)
   * {"event":"notice","data":{"notice_time":1636858644576432,"notice":{"data":{"group_id":1636613041610001,"im_id":10253,"update_at":1636858644},"notify_type":"ModifyMember"},"notice_id":1636858644576410}}
   */
  onGroupMemberInfoUpdate = async (data) => {
    const { group_id: groupId } = data.notice.data;
    await Promise.all([this.getGroupMemberList(groupId, false, false), this.getGroupInfo(groupId)]);
    global.emitter.emit(constant.event.groupMemberChange, { groupId, isLocal: true });
  };

  addGroupMembers = async (param) => {
    return ImService.addGroupMembers(param);
  };

  deleteGroupMembers = async (param) => {
    const res = await ImService.deleteGroupMembers(param);
    global.emitter.emit(constant.event.groupMemberChange, {
      groupId: param.groupId,
      isLocal: false,
    });
    return res;
  };

  setGroupRoles = async (param) => {
    return ImService.setGroupRoles(param);
  };

  /**
   * 更新群信息
   * @param {*} groupId
   * @param {*} params
   * @returns
   */
  @action
  updateGroupInfo = async (groupId, params) => {
    return ImService.updateGroupInfo(groupId, params);
  };

  /**
   * 修改群成员信息
   */
  @action
  managerUpdateGroupMemberInfo = async (groupId, imId, params) => {
    return ImService.managerUpdateGroupMemberInfo(groupId, imId, params);
  };

  getFileAuths = async (isRefresh) => {
    if (!isRefresh) {
      let { uploadAuth } = chatStore;
      const isInit = !uploadAuth;
      if (uploadAuth == null) {
        uploadAuth = await Session.getUploadAuth();
      }
      if (uploadAuth && uploadAuth.expiration > new Date().getTime()) {
        await this.initAliyunOSS(uploadAuth, isInit);
        return uploadAuth;
      }
    }
    const res = await ImService.getFileAuths();
    const uploadAuth = {
      bucket: res.bucket,
      region: res.region,
      endpoint: res.endpoint,
      accessKeyId: res.Auth.access_key_id,
      accessKeySecret: res.Auth.access_key_secret,
      stsToken: res.Auth.security_token,
      expiration: new Date().getTime() + 3000000, // 最大1小时过期，设置50分钟过期
    };
    await Session.setUploadAuth(uploadAuth);
    console.log('getFileAuths', uploadAuth);
    await this.initAliyunOSS(uploadAuth, true);
    return uploadAuth;
  };

  initAliyunOSS = async (uploadAuth, isInit) => {
    if (!isInit) return;
    AliyunOSS.initWithSecurityToken(
      uploadAuth.stsToken,
      uploadAuth.accessKeyId,
      uploadAuth.accessKeySecret,
      uploadAuth.endpoint,
      {
        maxRetryCount: 2,
        timeoutIntervalForRequest: 30,
        timeoutIntervalForResource: 24 * 60 * 60,
      }
    );
    chatStore.uploadAuth = uploadAuth;
    await promiseUtil.sleep(100);
  };

  uploadFile = async (path, isRefresh) => {
    try {
      const { bucket, endpoint } = await this.getFileAuths(isRefresh);
      const originFileName = path.substring(path.lastIndexOf('/') + 1);
      let fileName = originFileName;
      const index = fileName.lastIndexOf('.');
      if (index > -1) {
        fileName = `${new Date().getTime()}${fileName.substring(index)}`;
      } else {
        fileName = `${new Date().getTime()}.txt`;
      }
      const objectKey = `_/app/${deviceInfo.getUniqueID()}/${fileName}`;
      console.log('imAction uploadFile objectKey', objectKey, path);
      const r1 = await AliyunOSS.asyncUpload(bucket, objectKey, path);
      const fileUrl = `https://${bucket}.${endpoint}/${objectKey}`;
      console.log('imAction uploadFile', r1, fileUrl);
      return { fileUrl, originFileName, fileName, path };
    } catch (e) {
      logger.warn('uploadFile error', e, e?.message, path);
      if (
        !isRefresh &&
        e?.message &&
        [
          'The OSS Access Key Id you provided does not exist in our records.',
          'The security token you provided has expired.',
        ].indexOf(e.message) > -1
      ) {
        return this.uploadFile(path, true);
      }
      return Promise.reject(e);
    }
  };

  /**
   * 修改好友信息
   * @param {*} contactId
   * @param {*} params
   * @returns
   */
  @action
  changeContact = async (contactId, params) => {
    const { imId } = userStore;
    return ImService.changeContact(imId, contactId, params);
  };

  /**
   * 获取好友信息
   * @param {*} contactId
   * @param {*} params
   * @returns
   */
  @action
  getContact = async (contactId, params) => {
    const { imId } = userStore;
    return ImService.getContact(imId, contactId, params);
  };

  /**
   * 获取隐私设置
   */
  @action
  getPrivacySettings = async (imId) => {
    return ImService.getPrivacySettings(imId || userStore.imId);
  };

  /**
   * 修改隐私设置
   */
  @action
  updateFriendVerification = async (enable) => {
    const { imId } = userStore;
    return ImService.updatePrivacySettings(imId, { key: 'add_verify', value: enable ? 1 : 0 });
  };

  /**
   * 更新隐私设置
   */
  @action
  updatePrivacySettings = async (data) => {
    const { imId } = userStore;
    return ImService.updatePrivacySettings(imId, data);
  };

  /**
   * 黑名单列表
   */
  @action
  getBlacklist = async () => {
    const res = await ImService.getBlacklist(userStore.imId);
    return res.blacklists || [];
  };

  /**
   * 设置黑名单
   */
  @action
  setBlacklist = async (imId, isBlack) => {
    if (isBlack) {
      return ImService.addBlacklist(userStore.imId, [imId]);
    }
    return ImService.deleteBlacklist(userStore.imId, [imId]);
  };

  /**
   * 好友黑名单状态变更
   * {"event":"notice","data":{"notice_time":1638172306071882,"notice":{"data":{"im_id":10104,"timestamp":1638172306},"is_push":1,"notify_type":"rm_blacklist"},"notice_id":1638172306071810}}
   */
  @action
  updateFriendStatus = async (data) => {
    const { im_id } = data.notice.data;
    await chatAction.getFriend(im_id, false, true);
    global.emitter.emit(constant.event.blackMessage, { imId: im_id, data });
  };

  /**
   * 获取群设置
   */
  @action
  getGroupSettings = async (groupId) => {
    const res = await ImService.getGroupSettings(groupId);
    return res.setting || {};
  };

  /**
   * 获取群公告
   */
  @action
  getGroupNotices = async (groupId) => {
    const res = await ImService.getGroupNotices(groupId);
    return res || {};
  };

  /**
   * 新增群公告
   */
  @action
  postGroupNotices = async (groupId, data) => {
    const res = await ImService.postGroupNotices(groupId, data);
    return res || {};
  };

  /**
   * 修改群设置
   */
  @action
  updateGroupSettings = async (groupId, data) => {
    return ImService.updateGroupSettings(groupId, data);
  };

  /**
   * 修改群成员设置
   */
  @action
  updateGroupMemberSetting = async (groupId, imId, data) => {
    return ImService.updateGroupMemberSetting(groupId, imId, data);
  };

  /**
   * 修改群成员设置
   */
  @action
  getGroupMemberSettingList = async (groupId) => {
    return ImService.getGroupMemberSettingList(groupId);
  };

  /**
   * 解散群
   */
  @action
  deleteGroup = async (groupId) => {
    await ImService.deleteGroup(groupId);
    await this.onDeleteDataByGroupDisbanded(groupId);
  };

  /**
   * 群已解散，删除本地数据
   */
  @action
  onDeleteDataByGroupDisbanded = async (groupId) => {
    await Promise.all([
      this.deleteSession({ sessionId: groupId, isGroup: 1 }),
      groupMemberDao.cleanGroupMember({ groupId }),
      chatGroupDao.deleteGroup({ groupId }),
    ]);
    global.emitter.emit(constant.event.chatSessionChange);
  };

  /**
   * 收到解散群通知，暂不删除本地数据，等用户进入群聊再删除
   */
  @action
  onGroupDisbanded = async (data) => {
    const groupId = data.notice.group_id;
    await chatSessionDao.falseDeleteSession({
      sessionId: groupId,
      isGroup: 1,
      content: `[${I18n.t('page_chat_group_dismiss')}]`,
    });
    global.emitter.emit(constant.event.groupInfoChange, { groupId, isDelete: true });
  };

  @action
  getAgoraToken = async (channel, uid) => {
    return ImService.getAgoraToken(channel, uid);
  };

  /**
   * 发送广播 / 群发助手
   */
  @action
  sendBroadcast = async ({ content, ids }) => {
    const data = {
      seq: new Date().getTime(),
      action: constant.messageAction.msgP2P,
      data: {
        send_way: 1,
        receiver_ids: ids,
        msg: {
          content,
          type: constant.messageType.text,
          isBroadCast: true,
        },
        proxy_id: 0,
        parent_id: 0,
      },
    };
    const res = await ImService.sendBroadcast(data);
    console.log('imAction sendBroadcast', res);
    return res;
  };

  /**
   * 指定sessionId的时候搜索对应的消息列表，否则搜索每个会话的消息数量
   */
  queryMessageList = async ({ content, sessionId, session, result, limit }) => {
    if (!result) {
      result = {};
    }
    if (sessionId) {
      result.messageList = await messageDao.queryHistoryMessage({ sessionId, content });
      if (!result.memberMap) {
        result.memberMap = await this.getCurrentMessageMemberMap(session, true);
      }
      return result;
    }

    if (!result.messageTableSet) {
      result.messageTableSet = new Set();
      const messageTables = await messageDao.getMessageTableList();
      if (!messageTables.length) {
        return result;
      }
      messageTables.forEach((item) => result.messageTableSet.add(item.name));
    }

    const { imId } = userStore;
    if (!result.friends) {
      result.friends = await friendDao.getAllFriends({ ownerId: imId });
    }
    const friendResults = await this.queryMessageCountListByList({
      list: result.friends,
      sessionIdKey: 'imId',
      messageTableSet: result.messageTableSet,
      content,
      limit,
    });
    limit -= friendResults.length;
    if (limit >= 0) {
      if (!result.groups) {
        result.groups = await chatGroupDao.getGroups({ ownerId: imId });
      }
      const groupsResults = await this.queryMessageCountListByList({
        list: result.groups,
        sessionIdKey: 'groupId',
        messageTableSet: result.messageTableSet,
        content,
        limit,
      });
      result.sessionList = [...friendResults, ...groupsResults];
    } else {
      result.sessionList = friendResults;
    }
    return result;
  };

  /**
   * 指定sessionId的时候搜索对应的消息列表，否则搜索每个会话的消息数量
   */
  searMessageListBySession = async ({ content, sessionId, session, result, limit }) => {
    result = result || {};
    if (sessionId) {
      result.messageList = await messageDao.queryHistoryMessage({ sessionId, content });
      if (!result.memberMap) {
        result.memberMap = await this.getCurrentMessageMemberMap(session, true);
      }
      return result;
    }

    if (!result.messageTableSet) {
      result.messageTableSet = new Set();
      const messageTables = await messageDao.getMessageTableList();
      if (!messageTables.length) {
        return result;
      }
      messageTables.forEach((item) => result.messageTableSet.add(item.name));
    }

    if (!result.originSessionList) {
      const res = await this.searchChatSession({
        searchValue: '',
        limit: 10000,
      });
      result.originSessionList = res.sessionList;
    }

    result.sessionList = await this.queryMessageCountListByList({
      list: result.originSessionList,
      sessionIdKey: 'sessionId',
      messageTableSet: result.messageTableSet,
      content,
      limit,
    });
    return result;
  };

  /**
   * 搜索会话
   */
  searchChatSession = async ({ result, searchValue, limit }) => {
    result = result || {};
    if (!result.sessionList) {
      result.sessionList = await chatSessionDao.querySessionList();
    }
    result.data = result.sessionList
      .filter((item) => item.title?.toLowerCase().includes(searchValue))
      .slice(0, limit);
    return result;
  };

  queryMessageCountListByList = async ({ list, sessionIdKey, messageTableSet, content, limit }) => {
    const result = [];
    for (const item of list) {
      if (!messageTableSet.has(getChatMessageTableName(item.ownerId, item[sessionIdKey], false))) {
        continue;
      }
      item.messageCount = await messageDao.queryHistoryMessageCount({
        sessionId: item[sessionIdKey],
        content,
      });
      if (item.messageCount > 0) {
        result.push(item);
        if (result.length >= limit) {
          break;
        }
      }
    }
    return result;
  };

  /**
   * pc登录消息
   * {"event":"notice","data":{"notice_time":1683270261484777,"notice":{"data":{"mac":"14:f6:d8:b6:61:79","model":"windows_nt14:f6:d8:b6:61:79","name":"RAYS-PC","platform":"pc","req_token":"ebbc5468657caa1c53f22c6b219ebed8","status":"pending","timestamp":1683270261,"version":"Win10"},"is_push":1,"notify_type":"pc_login"},"notice_id":1683270261484710}}
   * @param data
   * @returns {Promise<void>}
   */
  onPCLogin = async (data) => {
    navigationService.push('loginPC', { data: data.notice.data });
  };

  /**
   * 同步公告
   */
  syncNotifications = async (dateline) => {
    const { imId, isManage } = userStore;
    if (isManage || !imId) return;
    const param = {
      dateline,
      limit: 50,
    };
    if (!param.dateline) {
      const localInfo = await Session.getPaymentInfo();
      param.dateline = localInfo?.lastNotifyTime;
    }
    if (!param.dateline) {
      param.dateline = moment().subtract(30, 'day').unix() * 1000;
    }
    const res = await ImService.getNotificationList(param);
    if (res.code !== 0 || !res.data?.length) return;
    res.data.forEach((item) => {
      item.receiver_id = imId;
    });
    await this.saveOfflineMessageList(res);
    dateline = res.data[res.data.length - 1].msg_time + 1;
    await Session.updatePaymentInfo('lastNotifyTime', dateline);
    if (res.data.length === param.limit) {
      return this.syncNotifications(dateline);
    }
  };

  sendGPT = async (param) => {
    return ChatService.sendGPT(param);
  };

  @action
  saveConversationItems = async (items) => {
    if (!items || !items.length) return;
    const conversations = items.map((item) => {
      const content = sentryUtil.parseSafe(item.content, 'conversation', true);
      console.log('content123', content);
      return {
        sessionId: `${userStore.imId}_${item.sessionId}`,
        receiverId: item.sessionId,
        talkMode: 1,
        updatedAt: item.lastMsgTime,
        unreadNum: item.unReadNum,
        MsgId: item.msgId,
        msgText: content.content,
        extra: JSON.stringify(item),
      };
    });
    console.log('conversations123123', conversations);
    await ImService.saveConversationItems(conversations);
  };

  @action
  queryConversationItems = async () => {
    return ImService.queryConversationItems();
  };

  @action
  deleteConversationItems = async (data) => {
    return ImService.deleteConversationItems(data);
  };
}

export default new ImAction();
