import Restful from './restful';
import qs from 'qs';
import userStore from '../store/stores/user';

class CompanyService {
  /**
   * 查询公司有没有IM账号
   */
  async hasIMAccount(employerId) {
    //return Restful.get(`/employers/${employerId}/public`);
    console.log('employerId', employerId);
    return false;
  }

  /**
   * 消费记录查询
   * request: /v1.0.0/employers/wallets/trades
   * method: GET
   *
   */
  async queryTrades(param) {
    return Restful.get(
      `/employers/wallets/trades?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 查询企业账户余额
   * request: /v1.0.0/employers/wallets/trades/amount
   * method: GET
   * eg.
   */
  async queryAmount() {
    return Restful.get(`/employers/wallets/trades/amount`);
  }

  /**
   * 企业端查询职位
   * request: /v1.0.0/employers/jobs
   * method: GET
   * eg.
   */
  async queryJobs(param) {
    return Restful.get(`/employers/jobs?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 添加职位
   * request: /v1.0.0/employers/jobs
   * method: POST
   * @param job - job
   */
  async addJobs(job) {
    return Restful.post(`/employers/jobs`, job);
  }

  /**
   * 企业端查询单个职位
   * request: /v1.0.0/employers/jobs/{jobId}
   * method: GET
   * @param jobId - jobId
   * eg.
   */
  async queryJobsByJobId(jobId) {
    return Restful.get(`/employers/jobs/${jobId}`);
  }

  /**
   * 编辑职位
   * request: /v1.0.0/employers/jobs/{jobId}
   * method: PUT
   * @param job - job
   * @param jobId - jobId
   */
  async editJobs(jobId, data) {
    return Restful.put(`/employers/jobs/${jobId}`, data);
  }

  /**
   * 删除职位
   * request: /v1.0.0/employers/jobs/{jobId}
   * method: DELETE
   * @param jobId - jobId
   */
  async deleteJobs(jobId) {
    return Restful.delete(`/employers/jobs/${jobId}`);
  }

  /**
   * 职位下线
   * request: /v1.0.0/employers/jobs/{jobId}/close
   * method: PUT
   * @param jobId - jobId
   */
  async closeJobs(jobId) {
    return Restful.put(`/employers/jobs/${jobId}/close`);
  }

  /**
   * 职位上线
   * request: /v1.0.0/employers/jobs/{jobId}/publish
   * method: PUT
   * @param jobId - jobId
   */
  async publishJobs(jobId, data) {
    return Restful.put(`/employers/jobs/${jobId}/publish`, data);
  }

  /**
   * 刷新职位，就是更改一下updateTime，使得职位排到顶部显示
   * request: /v1.0.0/employers/jobs/{jobId}/refresh
   * method: PUT
   * @param jobId - jobId
   */
  async refreshJobs(jobId) {
    return Restful.put(`/employers/jobs/${jobId}/refresh`);
  }

  /**
   * 智能刷新职位，body里面存一个 布尔值，格式: "autoRenew" : true
   * request: /v1.0.0/employers/jobs/{jobId}/auto/renew
   * method: PUT
   * @param autoRenewMap - autoRenewMap
   * @param jobId - jobId
   */
  async autoRenewJobs(autoRenewMap, jobId) {
    return Restful.put(`/employers/jobs/${jobId}/auto/renew`, autoRenewMap);
  }

  /**
   * 标记这个职位已经 沟通过了
   * request: /v1.0.0/employers/jobs/{jobId}/seekers/{seekerId}/communication
   * method: PUT
   * @param jobId - jobId
   * @param seekerId - seekerId
   */
  async communicationJobs(jobId, seekerId) {
    return Restful.put(`/employers/jobs/${jobId}/seekers/${seekerId}/communication`);
  }

  /**
   * 职位置顶续费
   * request: /v1.0.0/employers/jobs/{jobId}/top
   * method: PUT
   * @param jobId - jobId
   */
  async topJobs(jobId) {
    return Restful.put(`/employers/jobs/${jobId}/top`);
  }

  /**
   * 判断职位是否有服务包和服务包是否过期
   * request: /v1.0.0/employers/jobs/{jobId}/top
   * method: PUT
   * @param jobId - jobId
   */
  async hasServicePackage(jobId) {
    return Restful.get(`/employers/jobs/${jobId}/hasServicePackage`);
  }

  /**
   * 企业端查询 已购买的服务包
   * request: /v1.0.0/employers/products
   * method: GET
   */
  async queryProducts(param) {
    return Restful.get(`/employers/products?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 分页查询 可购买的服务包
   * request: /v1.0.0/employers/product/services
   * method: GET
   * eg.
   */
  async queryProductServices(param) {
    return Restful.get(
      `/employers/product/services?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 企业端查询 已购买的服务包
   * request: /v1.0.0/employers/products/items
   * method: GET
   * eg.
   */
  async queryProductItems(param) {
    return Restful.get(
      `/employers/products/items?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  async uploadImageByBase64(image) {
    return Restful.post(`/employers/jobs/map/images`, image);
  }

  /**
   * 公司主页信息编辑
   * request: /v1.0.0/employers/me
   * method: PUT
   * @param employer - employer
   */
  async editEmployer(employer) {
    return Restful.put(`/employers/me`, employer);
  }

  /**
   * 上传公司图片
   * request: /v1.0.0/employers/{employerId}/images
   * method: POST
   * @param employerId - employerId
   * @param image - image
   */
  async uploadCompanyImage(employerId, path) {
    // return Restful.upload(`/employers/${employerId}/images`, image);
    const formData = new FormData();
    const file = {
      uri: IS_IOS ? `file:///${path}` : path,
      type: 'multipart/form-data',
      name: 'image.jpg',
    };
    formData.append('image', file);
    return Restful.upload(`/employers/${employerId}/images`, formData);
  }

  /**
   * 上传公司logo
   * request: /v1.0.0/employers/{employerId}/logo
   * method: POST
   * @param employerId - employerId
   * @param logo - logo
   */
  async uploadCompanyLogo(employerId, logoPath) {
    const formData = new FormData();
    const file = {
      uri: IS_IOS ? `file:///${logoPath}` : logoPath,
      type: 'multipart/form-data',
      name: 'image.jpg',
    };
    formData.append('logo', file);
    return Restful.post(`/employers/${employerId}/logo`, formData);
  }

  /**
   * authorize
   * request: /v1.0.0/employers/authorize
   * method: POST
   * @param name - name
   * @param password - password
   */
  async authorize(name, password) {
    return Restful.post(`/employers/authorize`, { name, password });
  }

  /**
   * getEmployer
   * request: /v1.0.0/employers/me
   * method: GET
   * eg.
   */
  async getEmployer() {
    return Restful.get(`/employers/me`);
  }

  /**
   * register
   * request: /v1.0.0/employers/register
   * method: POST
   *
   */
  async register(employer) {
    return Restful.post(`/employers/register`, employer);
  }

  /**
   * setDefaultLanguage
   * request: /v1.0.0/employers/language
   * method: PUT
   * @param language - language
   */
  async setDefaultLanguage(language) {
    return Restful.put(`/employers/language`, language);
  }

  /**
   * change password
   * request: /v1.0.0/employers/password
   * method: PUT
   * @param changePassword - changePassword
   */
  async changePassword(changePassword) {
    return Restful.put(`/employers/password`, changePassword);
  }

  /**
   * authorizeMobile
   * request: /v1.0.0/employers/authorize/mobile
   * method: POST
   */
  async authorizeMobile(data) {
    return Restful.post(
      `/employers/authorize/mobile?${qs.stringify(data, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * sendVerifyCode
   * request: /v1.0.0/employers/sms/verify-code
   * method: POST
   * @param mobile - mobile
   * @param regionCode - regionCode
   */
  async sendVerifyCode(data) {
    return Restful.post(
      `/employers/sms/verify-code?${qs.stringify(data, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 公司更新 个人账户信息 以及 默认联系人信息
   * request: /v1.0.0/employers/me/and/contact
   * method: PUT
   * @param employer - employer
   */
  async updateEmployerAndContact(employer) {
    return Restful.put(`/employers/me/and/contact`, employer);
  }

  /**
   * 完善资质认证
   * request: /v1.0.0/employers/me/certification
   * method: PUT
   * @param employerCertificateParam - employerCertificateParam
   */
  async updateEmployerCertificate(employerCertificateParam) {
    return Restful.put(`/employers/me/certification`, employerCertificateParam);
  }

  /**
   * 企业 修改手机号码
   * request: /v1.0.0/employers/change/mobile
   * method: PUT
   * @param requestParam - requestParam
   */
  async changeMobile(requestParam) {
    return Restful.put(`/employers/change/mobile`, requestParam);
  }

  /**
   * 修改邮箱
   * request: /v1.0.0/employers/change/emails
   * method: PUT
   * @param requestParam - requestParam
   */
  async changeEmail(requestParam) {
    return Restful.put(`/employers/change/emails`, requestParam);
  }

  /**
   * 发送邮件验证码
   * request: /v1.0.0/employers/change/emails/{email}/send/codes
   * method: GET
   * @param email - email
   * eg.
   */
  async sendEmailCode(email) {
    return Restful.get(`/employers/change/emails/${email}/send/codes`);
  }

  /**
   * 获取 公司职位的统计数据
   * request: /v1.0.0/employers/jobs/statistics/status
   * method: GET
   * eg.
   */
  async getJobStatistics() {
    return Restful.get(`/employers/jobs/statistics/status`);
  }

  /**
   * 分页查询公司联系人
   * request: employers/contacts
   * @param {*} queryParams
   */
  async queryContacts(queryParams) {
    return Restful.get(
      `/employers/contacts?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 添加联系人
   * @param {*} params
   */
  async addContact(params) {
    return Restful.post(`/employers/contacts`, params);
  }

  /**
   * 编辑联系人
   * @param {*} params
   */
  async editContact(params) {
    return Restful.put(`/employers/contacts/${params.id}`, params);
  }

  /**
   * 删除联系人
   * @param {*} params
   */
  async deleteContact(id) {
    return Restful.delete(`/employers/contacts/${id}`);
  }

  /**
   * 设置联系人聊天状态
   * @param {*} id 联系人ID
   * @param {*} enabled 是否启用聊天
   */
  async setContactIM(id, enabled) {
    return Restful.put(`/employers/contacts/${id}/im`, { enabled });
  }

  /**
   * 设置联系人聊天设置
   * @param {*} id 联系人ID
   * @param {*} settings 聊天设置
   */
  async setContactChatSettings(id, settings) {
    return Restful.put(`/employers/contacts/${id}/chat-settings`, settings);
  }

  /**
   * 查询 余额
   * request: /v1.0.0/employers/wallets/balance
   * method: GET
   * eg.
   */
  async getBalance() {
    return Restful.get(`/employers/wallets/balance`);
  }

  /**
   * 获取 公司职位的统计数据
   * request: /v1.0.0/employers/statistics/jobs/status
   * method: GET
   * eg.
   */
  async getEmployersJobStatistics() {
    return Restful.get(`/employers/statistics/jobs/status`);
  }

  /**
   * 充值订单查询
   * request: /v1.0.0/employers/wallets/recharges
   * method: GET
   * eg.
   */
  async queryRecharges(param) {
    return Restful.get(
      `/employers/wallets/recharges?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 账户充值订单（积分/金币）
   * request: /v1.0.0/employers/wallets/recharges
   * method: POST
   * @param amount - amount
   * @param paymethod - paymethod
   */
  async createRecharge(queryParams) {
    return Restful.post(
      `/employers/wallets/recharges?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 充值支付
   * request: /v1.0.0/employers/wallets/recharges/{orderId}/pay
   * method: POST
   * @param orderId - orderId
   * @param orderPaymentRequest - orderPaymentRequest
   */
  async payRecharge(orderId, orderPaymentRequest) {
    return Restful.post(`/employers/wallets/recharges/${orderId}/pay`, orderPaymentRequest);
  }

  /**
   * 查询 充值订单的支付状态
   * request: /v1.0.0/employers/wallets/recharges/{rechargeId}/status
   * method: GET
   * @param rechargeId - rechargeId
   * eg.
   */
  async queryRechargeStatus(rechargeId) {
    return Restful.get(`/employers/wallets/recharges/${rechargeId}/status`);
  }

  /**
   * 取消 充值订单
   * request: /v1.0.0/employers/wallets/recharges/{rechargeId}/cancel
   * method: DELETE
   * @param rechargeId - rechargeId
   * eg.
   */
  async cancelRecharge(rechargeId) {
    return Restful.delete(`/employers/wallets/recharges/${rechargeId}/cancel`);
  }

  /**
   * 充值包查询
   * request: /v1.0.0/employers/wallets/recharges/bags/all
   * method: GET
   * eg.
   */
  async queryRechargeBags() {
    return Restful.get(`/employers/wallets/recharges/bags/all`);
  }

  /**
   * payway发起支付前需要调用的API，在生成订单之后调用
   * request: /v1.0.0/employers/wallets/recharges/${orderId}/payway/prepay
   * @param {*} orderId
   * @param {*} bodyParams
   */
  async paywayPrepay(orderId, bodyParams) {
    return Restful.post(`/employers/wallets/recharges/${orderId}/payway/prepay`, bodyParams);
  }

  /**
   * 企业查询收到的简历
   * request: /v1.0.0/employers/applications
   * method: GET
   * eg.
   */
  async queryApplications(param) {
    return Restful.get(`/employers/applications?${qs.stringify(param, { arrayFormat: 'repeat' })}`);
  }

  /**
   * 面试邀请记录
   * request: /v1.0.0/employers/applications/interviews
   * method: GET
   * eg.
   */
  async queryInterviews(param) {
    return Restful.get(
      `/employers/applications/interviews?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 查询各个筛选状态的数量
   * request: /v1.0.0/employers/applications/statistics
   * method: GET
   * eg.
   */
  async getApplicationsStatistics(param) {
    return Restful.get(
      `/employers/applications/statistics?${qs.stringify(param, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 查询简历备注
   * request: /v1.0.0/employers/applications/{jobApplyId}/comments
   * method: GET
   * @param jobApplyId - jobApplyId
   * eg.
   */
  async queryComments(jobApplyId) {
    return Restful.get(`/employers/applications/${jobApplyId}/comments`);
  }

  /**
   * 给投递的简历添加备注
   * request: /v1.0.0/employers/applications/{jobApplyId}/comments
   * method: POST
   * @param jobApplyComments - jobApplyComments
   * @param jobApplyId - jobApplyId
   */
  async addComments(jobApplyId, jobApplyComments) {
    return Restful.post(`/employers/applications/${jobApplyId}/comments`, jobApplyComments);
  }

  /**
   * 修改简历备注
   * request: /v1.0.0/employers/applications/{jobApplyId}/comments/{commentsId}
   * method: PUT
   * @param commentsId - commentsId
   * @param contactId - 联系人id
   * @param content - 备注内容
   * @param employerId - 企业id
   * @param jobApplyId - jobApplyId
   * @param jobApplyId - 职位申请id
   */
  async editComments(jobApplyId, commentsId, queryParams) {
    return Restful.put(`/employers/applications/${jobApplyId}/comments/${commentsId}`, queryParams);
  }

  /**
   * 删除简历备注
   * request: /v1.0.0/employers/applications/{jobApplyId}/comments/{commentsId}
   * method: DELETE
   * @param commentsId - commentsId
   * @param jobApplyId - jobApplyId
   */
  async deleteComments(jobApplyId, commentsId) {
    return Restful.delete(`/employers/applications/${jobApplyId}/comments/${commentsId}`);
  }

  /**
   * 推荐简历
   * request: /v1.0.0/employers/resumes/recommend
   * method: GET
   * @param applyType - 简历来源：用户投递 0，企业查找 1,全部的话传-1 常量两：JOB_APPLY_TYPE
   * @param deliveryDays - 投递时间
   * @param expectLocationId - 期望工作地点，常量：LOCATION
   * @param fromAge - 最小年龄
   * @param fromExperienceYear - 最低工作年限
   * @param jobIds - 职位id
   * @param keyWord - 关键词
   * @param limit -
   * @param liveLocationId - 现居住地址,常量：LOCATION
   * @param offset -
   * @param order -
   * @param page -
   * @param qualificationId - 学历 常量：QUALIFICATION
   * @param query -
   * @param sexId - 性别，常量：SEX
   * @param size -
   * @param toAge - 最大年龄
   * @param toExperienceYear - 最高工作年限
   * eg.
   */
  async recommendResumes(queryParams) {
    return Restful.get(
      `/employers/resumes/recommend?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 人才搜索
   * request: /v1.0.0/employers/resumes/search
   * method: GET
   * @param applyType - 简历来源：用户投递 0，企业查找 1,全部的话传-1 常量两：JOB_APPLY_TYPE
   * @param deliverTime - 投递时间
   * @param expectLocationId - 期望工作地点
   * @param fromAge - 最小年龄
   * @param fromExperienceYear - 最低工作年限
   * @param jobId - 职位id
   * @param jobTitle - 关键词
   * @param limit -
   * @param liveLocationId - 现居住地址
   * @param offset -
   * @param order -
   * @param page -
   * @param qualificationId - 学历，常量：QUALIFICATION
   * @param query -
   * @param sexId - 性别，常量：SEX
   * @param size -
   * @param toAge - 最大年龄
   * @param toExperienceYear - 最高工作年限
   * eg.
   */
  async searchResumes(queryParams) {
    return Restful.get(
      `/employers/resumes/search?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }

  /**
   * 更改简历标签
   * request: /v1.0.0/employers/resumes/tags
   * method: PUT
   * @param cvTag - cvTag
   */
  async updateResumeTags(bodyParams) {
    return Restful.put(`/employers/resumes/tags`, bodyParams);
  }

  /**
   * 查询各个标签下的统计数量
   * request: /v1.0.0/employers/resumes/tags/types/{type}/statistics
   * method: GET
   * @param type - type
   * eg.
   */
  async getResumeTagsStatistics(type) {
    return Restful.get(`/employers/resumes/tags/types/${type}/statistics`);
  }

  /**
   * 删除简历标签
   * request: /v1.0.0/employers/resumes/tags/{cvId}
   * method: DELETE
   * @param cvId - cvId
   */
  async deleteResumeTags(cvId) {
    return Restful.delete(`/employers/resumes/tags/${cvId}`);
  }

  /**
   * 简历查看
   * request: /v1.0.0/employers/resumes/{resumeId}
   * method: GET
   * @param jobApplyId - jobApplyId
   * @param resumeId - resumeId
   * eg.
   */
  async getResumeDetail(resumeId) {
    return Restful.get(`/employers/resumes/${resumeId}`);
  }
  /**
   * 批量更改申请记录状态，待沟通，不合适
   * request: /v1.0.0/employers/status/{jobApplyStatus}
   * method: PUT
   * @param jobApplyIds - jobApplyIds
   * @param jobApplyStatus - jobApplyStatus
   */
  async updateJobApplyStatus(jobApplyStatus, jobApplyIds) {
    return Restful.put(`/employers/status/${jobApplyStatus}`, jobApplyIds);
  }
  /**
   * 邀请面试，可以选择简历(jobApplyId) 后 邀请。也可以不选简历 就 邀请
   * request: /v1.0.0/employers/interview
   * method: PUT
   * @param interviewParam - interviewParam
   */
  async inviteInterview(interviewParam) {
    return Restful.put(`/employers/interview`, interviewParam);
  }
  /**
   * 更改申请记录的状态
   * request: /v1.0.0/employers/{jobApplyId}/status/{jobApplyStatus}
   * method: PUT
   * @param jobApplyId - jobApplyId
   * @param jobApplyStatus - jobApplyStatus
   */
  async updateJobApplyStatusById(jobApplyId, jobApplyStatus) {
    return Restful.put(`/employers/${jobApplyId}/status/${jobApplyStatus}`);
  }
  /**
   * 面试邀请详细信息查询
   * request: /v1.0.0/employers/applications/{jobApplyId}/interviews
   * method: GET
   * @param jobApplyId - jobApplyId
   * eg.
   */
  async getInterviewDetail(jobApplyId) {
    return Restful.get(`/employers/applications/${jobApplyId}/interviews`);
  }
  /**
   * 变更面试状态
   * request: /v1.0.0/employers/applications/{jobApplyId}/interview/status/{interviewStatus}
   * method: PUT
   * @param interviewStatus - interviewStatus
   * @param jobApplyId - jobApplyId
   */
  async updateInterviewStatus(jobApplyId, interviewStatus) {
    return Restful.put(`/employers/applications/${jobApplyId}/interview/status/${interviewStatus}`);
  }
  /**
   * 简历收藏
   * request: /v1.0.0/employers/resumes/{resumeId}/follow
   * method: POST
   * @param resumeId - resumeId
   */
  async followResume(resumeId) {
    return Restful.post(`/employers/resumes/${resumeId}/follow`);
  }
  /**
   * 删除简历收藏
   * request: /v1.0.0/employers/resumes/{resumeId}/follow/{followCvId}
   * method: DELETE
   * @param followCvId - followCvId
   */
  async deleteFollowResume(resumeId, followCvId) {
    return Restful.delete(`/employers/resumes/${resumeId}/follow/${followCvId}`);
  }
  /**
   * 查询简历收藏
   * request: /v1.0.0/employers/resumes/follow
   * method: GET
   * eg.
   */
  async queryFollowResumes(queryParams) {
    return Restful.get(
      `/employers/resumes/follow?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }
  /**
   * 給该条职位申请的用户进行评价
   * request: /v1.0.0/employers/applications/{jobApplyId}/comments
   * method: PUT
   * @param commentId - 常量COMMENT_LEVEL
   * @param comments - comments
   * @param jobApplyId - jobApplyId
   */
  async addCommentsForJobApply(jobApplyId, data) {
    return Restful.put(
      `/employers/applications/${jobApplyId}/comments?${qs.stringify(data, {
        arrayFormat: 'repeat',
      })}`
    );
  }
  /**
   * 购买简历
   * request: /v1.0.0/employers/resumes/${resumeId}/buy
   * method: POST
   * eg.
   */
  async buyResume(data) {
    return Restful.post(
      `/employers/resumes/${data.resumeId}/buy?${qs.stringify(data, {
        arrayFormat: 'repeat',
      })}`
    );
  }

  /**
   * 企业购买服务包
   * request: /v1.0.0/employers/products/purchase
   * method: POST
   * @param employerBuyProduct - employerBuyProduct
   */
  async buyProduct(employerBuyProduct) {
    return Restful.post(`/employers/products/purchase`, employerBuyProduct);
  }

  /**
   * 查询 可用支付方式
   * request: /v1.0.0/employers/wallets/payment/gateway/all
   * method: GET
   * eg.
   */
  async queryPaymentGateway() {
    return Restful.get(`/employers/wallets/payment/gateway/all`);
  }

  /**
   * 获取 开发票税率
   * request: /v1.0.0/business/configurations/invoicechargerate
   * method: GET
   * eg.
   */
  async queryInvoiceChargeRate() {
    return Restful.get(`/business/configurations/invoicechargerate`);
  }

  /**
   * 分页+条件查询收件箱内消息
   * request: /v1.0.0/employers/messages/inbox
   * method: GET
   * eg.
   */
  async queryInboxMessages(queryParams) {
    return Restful.get(
      `/employers/messages/inbox?${qs.stringify(queryParams, { arrayFormat: 'repeat' })}`
    );
  }
  /**
   * 统计信息
   * request: /v1.0.0/employers/messages/statistics
   * method: GET
   * eg.
   */
  async queryMessagesStatistics() {
    return Restful.get(`/employers/messages/statistics`);
  }

  /**
   * 标记单条站内信已读
   * request: /v1.0.0/employers/messages/{messageId}
   * method: PUT
   * @param messageId - messageId
   */
  async readMessage(messageId) {
    return Restful.put(`/employers/messages/${messageId}`);
  }
  /**
   * 查询企业IM快捷语
   * request: /v1.0.0/employers/im/quick-text
   * method: GET
   */
  async queryQuickText() {
    if (userStore.isCompany) {
      return Restful.get(`/employers/im/quick-text`);
    }
    return Restful.get(`/users/im/quick-text`);
  }
  /**
   * 保存企业IM快捷语
   * request: /v1.0.0/employers/im/quick-text
   * method: POST
   * @param quickText - quickText
   */
  async saveQuickText(quickText) {
    if (userStore.isCompany) {
      return Restful.post(`/employers/im/quick-text`, quickText);
    }
    return Restful.post(`/users/im/quick-text`, quickText);
  }
  /**
   * 获取企业账号IM配置
   * request: /v1.0.0/employers/im/config
   * method: GET
   */
  async getEmployerImConfig() {
    return Restful.get(`/employers/im/config`);
  }
}

export default new CompanyService();
