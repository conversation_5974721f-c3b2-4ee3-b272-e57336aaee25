import Token from '../common/token';
import constant from '../store/constant';
import userStore from '../store/stores/user';

const TOKEN = 'token';
const USER = 'user';
const IS_LOGIN = 'isLogin';
const LANGUAGE = 'language';
const access_token_object = 'accessTokenObject';
const refresh_token_object = 'refreshTokenObject';
// 最后一条动态信息
const LAST_DYNAMIC = 'lastDynamic';
// 上一条动态信息
const PRE_LAST_DYNAMIC = 'preLastDynamic';
// 最后一条谁看过我
const LAST_SEEN = 'lastSeen';
// 上一条谁看过我
const PRE_LAST_SEEN = 'preLastSeen';
// 是否已经上传过设备信息
const UPLOADED_DEVICE = 'uploadDevice';

const IM_LOGIN_INFO = 'imLoginInfo';
const IM_ACCESS_TOKEN_OBJECT = 'imAccessTokenObject';
const UPLOAD_AUTH = 'uploadAuth';
const USER_SETTINGS = 'userSettings';
const PERSON_UNFO = 'personInfo';
const SELECTED_IM_ACCOUNT = 'selectedImAccount';

export const UserSettingKeys = {
  syncSelfMessageList: 'syncSelfMessageList',
  imUserRes: 'imUserRes',
};

function parseStringValue(value, defaultValue = value) {
  if (!value) return defaultValue;
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch (e) {}
  }
  return value;
}

function toStringValue(value) {
  if (!value || typeof value === 'string') return value;
  return JSON.stringify(value);
}

function getResObj(res) {
  return parseStringValue(res, {});
}

function getErrorObj() {
  return {};
}

function getErrorNull() {
  return null;
}

/**
 * session管理
 * 主要是登录信息保存
 */
class Session {
  async setToken(token) {
    return global.storage.save({ key: TOKEN, data: token });
  }

  async getToken() {
    const token = await this.getAccessToken();
    return token ? token.token : null;
  }

  async setIsLogin(value) {
    return global.storage.save({ key: IS_LOGIN, data: value });
  }

  async isLogin() {
    return global.storage.load({ key: IS_LOGIN }).catch(getErrorNull);
  }

  async setUser(user) {
    this._user = user;
    return user
      ? global.storage.save({ key: USER, data: user })
      : global.storage.remove({ key: USER });
  }

  async getUser() {
    if (!this._user) {
      this._user = await global.storage.load({ key: USER }).catch(getErrorNull);
    }
    return this._user;
  }

  async setLanguage(language) {
    return global.storage.save({ key: LANGUAGE, data: language });
  }

  async getLanguage() {
    return global.storage.load({ key: LANGUAGE }).catch(getErrorNull);
  }

  /**
   * 获取access token对象
   */
  async getAccessToken() {
    const token = await global.storage.load({ key: access_token_object }).catch(getErrorNull);
    if (token) {
      const json = token;
      return new Token(json.token, json.expireTime);
    }
    return null;
  }

  /**
   * 保存access token对象
   * 过期时间，因网络延迟等，将实际过期时间提前60秒，以防止临界点
   * @param {*} access_token
   * @param {*} expires_in 过期时间，秒
   */
  async saveAccessToken(access_token, expires_in) {
    const expireTime = Date.now() + (expires_in > 0 ? expires_in - 60 : 7200 - 60) * 1000;
    return this.setAccessToken(access_token, expireTime);
  }

  async setAccessToken(access_token, expireTime) {
    const token = new Token(access_token, expireTime);
    await global.storage.save({ key: access_token_object, data: token });
    return token;
  }

  /**
   * 获取refresh token对象
   */
  async getRefreshToken() {
    const token = await global.storage.load({ key: refresh_token_object }).catch(getErrorNull);
    if (token) {
      const json = token;
      return new Token(json.token, json.expireTime);
    }
    return null;
  }

  /**
   * 保存refresh token对象
   * 过期时间，将实际过期时间提前60秒，以防止临界点
   * 默认一个星期过期
   * @param {*} fresh_token
   */
  async saveRefreshToken(fresh_token) {
    const expireTime = Date.now() + (7 * 24 * 60 * 60 - 60) * 1000;
    const token = new Token(fresh_token, expireTime);
    await global.storage.save({ key: refresh_token_object, data: token });
    return token;
  }

  async setLastDynamice(value) {
    return global.storage.save({ key: LAST_DYNAMIC, data: value });
  }

  async getLastDynamic() {
    return global.storage.load({ key: LAST_DYNAMIC }).catch(getErrorNull);
  }

  async setPreLastDynamice(value) {
    return global.storage.save({ key: PRE_LAST_DYNAMIC, data: value });
  }

  async getPreLastDynamic() {
    return global.storage.load({ key: PRE_LAST_DYNAMIC }).catch(getErrorNull);
  }

  async setLastSeen(value) {
    return global.storage.save({ key: LAST_SEEN, data: value });
  }

  async getLastSeen() {
    return global.storage.load({ key: LAST_SEEN }).catch(getErrorNull);
  }

  async setPreLastSeen(value) {
    return global.storage.save({ key: PRE_LAST_SEEN, data: value });
  }

  async getPreLastSeen() {
    return global.storage.load({ key: PRE_LAST_SEEN }).catch(getErrorNull);
  }

  async setUploadDevice(result) {
    return global.storage.save({ key: UPLOADED_DEVICE, data: result });
  }

  async getUploadDevice() {
    return global.storage.load({ key: UPLOADED_DEVICE }).catch(getErrorNull);
  }

  /**
   * 保存用户设置，不传key时value是所有设置，传key时value是对应设置
   */
  async setUserSettings(key, value, userId = userStore.imId) {
    if (!userId) throw new Error('userId is null');
    console.log('setUserSettings', key, value);
    let data = value;
    if (key) {
      data = await this.getUserSettings(null, null, userId);
      data[key] = value;
    }
    return global.storage.save({
      key: USER_SETTINGS,
      id: userId.toString(),
      data: toStringValue(data),
    });
  }

  /**
   * 获取用户设置，不传key时获取所有设置，传key获取指定配置
   */
  async getUserSettings(key, defaultValue, userId = userStore.imId) {
    if (!userId) throw new Error('userId is null');
    const data = await global.storage
      .load({ key: USER_SETTINGS, id: userId.toString() })
      .then(getResObj, getErrorObj);
    if (key) {
      return data[key] === undefined ? defaultValue : data[key];
    }
    return data;
  }

  async setImAccessToken(accessToken, expireTime) {
    if (!accessToken) {
      this.imAccessToken = null;
      await global.storage.remove({ key: IM_ACCESS_TOKEN_OBJECT });
      global.emitter.emit(constant.event.imAccessTokenChange, null);
      return null;
    }
    if (accessToken.includes(' ')) {
      const arr = accessToken.match(/(\w+\s+)?(.+)/);
      if (arr?.length > 2) {
        accessToken = arr[2];
        console.log('saveAccessToken 2', accessToken);
      }
    }
    // 服务端在token过期前3天可获取新的token，我们提前71小时获取新token
    const token = new Token(accessToken, (expireTime - 255600) * 1000);
    this.imAccessToken = token;
    await global.storage.save({ key: IM_ACCESS_TOKEN_OBJECT, data: token });
    global.emitter.emit(constant.event.imAccessTokenChange, token);
    return token;
  }

  async getImAccessToken() {
    if (this.imAccessToken) return this.imAccessToken;
    let token = await global.storage.load({ key: IM_ACCESS_TOKEN_OBJECT }).catch(getErrorNull);
    if (token) {
      token = new Token(token.token, token.expireTime);
    }
    this.imAccessToken = token;
    return token;
  }

  async setImLoginInfo(user) {
    if (!user) {
      return global.storage.remove({ key: IM_LOGIN_INFO });
    }
    return global.storage.save({ key: IM_LOGIN_INFO, data: user });
  }

  async getImLoginInfo() {
    return global.storage.load({ key: IM_LOGIN_INFO }).catch(getErrorNull);
  }

  async setUploadAuth(data) {
    return global.storage
      .save({
        key: UPLOAD_AUTH,
        data,
      })
      .catch(getErrorNull);
  }

  async getUploadAuth() {
    return global.storage.load({ key: UPLOAD_AUTH }).catch(getErrorNull);
  }

  setPersonInfo = async (data) => {
    return global.storage.save({ key: PERSON_UNFO, data });
  };

  getPersonInfo = async () => {
    return global.storage.load({ key: PERSON_UNFO }).catch(getErrorNull);
  };

  /**
   * 设置选中的IM账号
   * @param {*} account 选中的IM账号对象
   */
  async setSelectedImAccount(account) {
    const key = SELECTED_IM_ACCOUNT;

    if (account) {
      return global.storage.save({ key, data: account });
    } else {
      return global.storage.remove({ key });
    }
  }

  /**
   * 获取选中的IM账号
   */
  async getSelectedImAccount() {
    const key = SELECTED_IM_ACCOUNT;

    return global.storage.load({ key }).catch(getErrorNull);
  }

  async clear() {
    global.storage.remove({ key: IS_LOGIN });
    global.storage.remove({ key: TOKEN });
    this.setUser(null);
    global.storage.remove({ key: access_token_object });
    global.storage.remove({ key: refresh_token_object });
    global.storage.remove({ key: LAST_DYNAMIC });
    global.storage.remove({ key: PRE_LAST_DYNAMIC });
    global.storage.remove({ key: LAST_SEEN });
    global.storage.remove({ key: PRE_LAST_SEEN });
    global.storage.remove({ key: IM_LOGIN_INFO });
    this.setImAccessToken(null);
    global.storage.remove({ key: UPLOAD_AUTH });
    global.storage.remove({ key: PERSON_UNFO });
    // 清除选中的IM账号
    global.storage.remove({ key: SELECTED_IM_ACCOUNT });
  }
}

export default new Session();
