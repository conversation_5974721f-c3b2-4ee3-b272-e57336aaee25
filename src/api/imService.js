import Restful from './imRestful';
import deviceInfo from '../util/deviceInfo';

/**
 * im相关API
 */
class ImService {
  /**
   * 发送广播 / 群发助手
   */
  async sendBroadcast(data) {
    data.seq = new Date().getTime();
    return Restful.post('/im/user/sendP2MMsg', data, { useBaseUrl: true, isIm: true });
  }

  /**
   * 获取管理员发的公告列表
   */
  async getNotificationList(data) {
    return Restful.post('/im/user/serviceNotification', data, { useBaseUrl: true, isIm: true });
  }

  /**
   * 获取消息列表，通过 imId 或 groupId
   * {"limit":10,"start_msg_id":1,"end_msg_id":9007199254740991,"im_id":501693}
   * {"limit":10,"start_msg_id":1,"end_msg_id":9007199254740991,"group_id":1693989822410001}
   */
  async getMsgList(data) {
    if (data.group_id) {
      data.device_id = deviceInfo.getUniqueID();
      return Restful.post('/im/groups/getGroupMsgByGroupID', data, {
        useBaseUrl: true,
        isIm: true,
      });
    }
    return Restful.post('/im/user/getMsgByImID', data, { useBaseUrl: true, isIm: true });
  }

  /**
   * 获取离线消息列表 或者 确认消息
   * {"limit":100,"msg_id":0,"seq":1}
   * {"limit":1,"msg_id":1634637046684310,"seq":1}
   *
   * {"device_id":"00000000-5250-c654-ffff-ffffef05ac4a","limit":1,"msg_id":1635305237675410,"seq":1}
   * {"device_id":"00000000-5250-c654-ffff-ffffef05ac4a","limit":100,"msg_id":0,"seq":1}
   *
   * {"code":0,"msg":"OK","ack":0,"data":null}
   */
  async getOfflineMsg(data, isGroup) {
    data.seq = 1;
    if (isGroup) {
      data.device_id = deviceInfo.getUniqueID();
      return Restful.post('/im/groups/getGroupMsgOffline', data, { useBaseUrl: true, isIm: true });
    }
    return Restful.post('/im/user/getMsg', data, { useBaseUrl: true, isIm: true });
  }

  /**
   * 获取离线通知列表 或者 确认通知
   * {"limit":100,"notice_id":0,"seq":1}
   * {"limit":1,"notice_id":1634637046684310,"seq":1}
   *
   * {"device_id":"00000000-5250-c654-ffff-ffffef05ac4a","limit":1,"notice_id":1635305237675410,"seq":1}
   * {"device_id":"00000000-5250-c654-ffff-ffffef05ac4a","limit":100,"msg_id":0,"seq":1}
   *
   * {"code":0,"msg":"OK","ack":0,"data":null}
   */
  async getOfflineNotice(data, isGroup) {
    data.seq = 1;
    if (isGroup) {
      data.device_id = deviceInfo.getUniqueID();
      return Restful.post('/im/groups/getOfflineNotice', data, { useBaseUrl: true, isIm: true });
    }
    return Restful.post('/im/user/getNotice', data, { useBaseUrl: true, isIm: true });
  }

  /**
   * 创建群聊
   * {"group_type":"chat","im_ids":[10102,10103,10115,10116,10104],"name":"R4080、R10102、R10103、sxw002、金水区的小姐姐"}
   * {"code":200,"message":"","data":{"group_id":**********110001,"name":"R10102、R10103、R4080、sxw002","create_at":**********,"group_type":"chat","max_cap":200,"avatar":"","creator_id":10104,"owner_id":10104,"owner":"","member_hash":"abf0931987f2f8eb7a8d26f2c21fe172","count":5}}
   */
  async createGroup(data) {
    data.group_type = 'chat';
    return Restful.post('/groups', data, { useBaseUrl: true });
  }

  /**
   * 获取群列表
   * {"code":200,"message":"","data":{"groups":[{"group_id":1635303910810001,"name":"测试群11","avatar":"","role":"owner","count":4,"max_cap":200,"member_hash":"30c20118492572a65cf26e46423391b3","is_vip":0}]}}
   */
  async getGroupList() {
    return Restful.get('/groups', null, { useBaseUrl: true });
  }

  /**
   * 解散群
   */
  async deleteGroup(groupId) {
    return Restful.delete(`/groups/${groupId}`, null, { useBaseUrl: true });
  }

  /**
   * 获取群信息
   * {"code":200,"message":"","data":{"group_id":**********110001,"name":"R4080、R10102、R10103、sxw002、金水区的小姐姐","create_at":**********,"update_at":**********,"group_type":"chat","sub_type":"","max_cap":200,"creator_id":10104,"owner_id":10104,"avatar":"f00cc991-c47a-4a14-956f-cc12d8e4dc94","member_hash":"8e816baf0fca5d4c39c111e59c1f2233","count":5,"activity_time":0,"is_vip":0,"vip_level":0,"tags":"","setting":{"update_at":**********,"no_speaking":0,"add_in_group":1,"invite":1,"invite_way":1,"public_group":0,"add_group_type":2}}}
   */
  async getGroupInfo(groupId) {
    return Restful.get(`/groups/${groupId}/info`, null, { useBaseUrl: true });
  }

  /**
   * 获取群公告
   * {"code":200,"message":"","data":{"notice":{"notice_id":2,"notice":"ddgg","updated_at":1635305932,"modify_by":10104}}}
   */
  async getGroupNotices(groupId) {
    return Restful.get(`/groups/${groupId}/notices`, null, { useBaseUrl: true });
  }

  /**
   * 新增群公告
   */
  async postGroupNotices(groupId, data) {
    return Restful.post(`/groups/${groupId}/notices`, data, { useBaseUrl: true });
  }

  /**
   * 获取群成员
   * {"code":200,"message":"","data":{"total_count":5,"member_hash":"8e816baf0fca5d4c39c111e59c1f2233","members":[{"join_at":**********,"group_nickname":"","role":"","add_way":"invite","add_by":10104,"add_from":"","account":"r0wgdh5910","account_alias":"R10001","im_id":10102,"nickname":"R10102","platform":"android","country_code":"","phone":"","sex":"","birthday":"","avatar":"http://**************:80/1/api/v1/files/f082c8f5-b15e-4385-9702-0ae03d37c054","address":"","signature":"","password":true}]}}
   */
  async getGroupMembers(groupId) {
    return Restful.get(`/groups/${groupId}/members`, null, { useBaseUrl: true });
  }

  /**
   * 新增群成员
   * {"im_ids":[10102]}
   * {"code":200,"message":"","data":{}}
   */
  async addGroupMembers({ groupId, imIds }) {
    return Restful.post(`/groups/${groupId}/members`, { im_ids: imIds }, { useBaseUrl: true });
  }

  /**
   * 删除群成员
   * {"im_ids":[10102]}
   * {"code":200,"message":"","data":{}}
   */
  async deleteGroupMembers({ groupId, imIds }) {
    return Restful.delete(`/groups/${groupId}/members`, { im_ids: imIds }, { useBaseUrl: true });
  }

  /**
   * 退出群
   */
  async exitGroup({ groupId, imId }) {
    return Restful.delete(`/groups/${groupId}/members/${imId}`, null, { useBaseUrl: true });
  }

  /**
   * 设置群成员角色
   * {"im_ids":[10102],"role":"owner"}
   */
  async setGroupRoles({ groupId, imIds, role }) {
    return Restful.post(`/groups/${groupId}/roles`, { im_ids: imIds, role }, { useBaseUrl: true });
  }

  /**
   * 获取我在群里的角色
   * {"code":200,"message":"","data":{"roles":[{"im_id":10104,"role":"owner","no_speaking":1,"modify_info":1,"add_in_group":1}]}}
   */
  async getGroupRoles(groupId) {
    const res = await Restful.get(`/groups/${groupId}/roles?role=admin`, null, {
      useBaseUrl: true,
    });
    return res?.roles || [];
  }

  /**
   * 获取群设置
   * {"code":200,"message":"","data":{"setting":{"update_at":**********,"no_speaking":0,"add_in_group":1,"invite":1,"invite_way":1,"public_group":0,"add_group_type":2}}}
   */
  async getGroupSettings(groupId) {
    return Restful.get(`/groups/${groupId}/settings`, null, { useBaseUrl: true });
  }

  /**
   * 修改群设置
   * {"key":"invite","value":0}
   */
  async updateGroupSettings(groupId, data) {
    return Restful.post(`/groups/${groupId}/settings`, data, { useBaseUrl: true });
  }

  /**
   * 修改群信息-(群名称、群头像等)
   */
  async updateGroupInfo(groupId, params) {
    return Restful.post(`/groups/${groupId}`, params, { useBaseUrl: true });
  }

  /**
   * 设置群成员信息
   */
  async managerUpdateGroupMemberInfo(groupId, imId, params) {
    return Restful.post(`/groups/${groupId}/members/${imId}`, params, {
      useBaseUrl: true,
    });
  }

  /**
   * 修改群成员设置
   */
  async updateGroupMemberSetting(groupId, imId, params) {
    return Restful.post(`/groups/${groupId}/members/${imId}/settings`, params, {
      useBaseUrl: true,
    });
  }

  /**
   * 获取群成员设置
   */
  async getGroupMemberSetting(groupId, imId) {
    return Restful.get(`/groups/${groupId}/members/${imId}/settings`, null, { useBaseUrl: true });
  }

  /**
   * 获取群成员设置
   */
  async getGroupMemberSettingList(groupId) {
    return Restful.get(`/groups/${groupId}/member_settings`, null, { useBaseUrl: true });
  }

  /**
   * 获取上传文件的token
   * @returns
   */
  async getFileAuths() {
    return Restful.get('/file_auths', null, { useBaseUrl: true });
  }

  /**
   * 修改联系人信息
   * @param {*} id
   * @param {*} cid
   * @param {*} params
   * @returns
   */
  async changeContact(id, cid, params) {
    return Restful.patch(`/users/${id}/contacts/${cid}`, params, { useBaseUrl: true });
  }

  /**
   * 获取联系人信息
   * @param {*} id
   * @param {*} cid
   * @param {*} params
   * @returns
   */
  async getContact(id, cid, params) {
    return Restful.get(`/users/${id}/contacts/${cid}`, params, { useBaseUrl: true });
  }

  /**
   * 获取隐私设置
   */
  async getPrivacySettings(id) {
    return Restful.get(`/users/${id}/privacy_settings`, null, { useBaseUrl: true });
  }

  /**
   * 修改隐私设置
   */
  async updatePrivacySettings(id, params) {
    return Restful.post(`/users/${id}/privacy_settings`, params, { useBaseUrl: true });
  }

  /**
   * 获取黑名单列表
   * {"code":200,"message":"","data":{"blacklists":null}}
   * {"code":200,"message":"","data":{"blacklists":[{"im_id":10252,"blacklist_type":1,"nickname":"R5093","friend_nickname":"","avatar":""}]}}
   */
  async getBlacklist(id) {
    return Restful.get(`/users/${id}/blacklist`, null, { useBaseUrl: true });
  }

  /**
   * 加入黑名单
   * {"blacklist":[10252]}
   */
  async addBlacklist(id, blacklist) {
    return Restful.post(`/users/${id}/blacklist`, { blacklist }, { useBaseUrl: true });
  }

  /**
   * 移出黑名单
   * {"blacklist":[10252]}
   */
  async deleteBlacklist(id, blacklist) {
    return Restful.delete(`/users/${id}/blacklist`, { blacklist }, { useBaseUrl: true });
  }

  /**
   * 获取agoratoken
   */
  async getAgoraToken(channel, uid) {
    return Restful.get(
      `/agoras/rtc_token`,
      { channel_name: channel, role: 'publisher', tokent_ype: 'uid', uid, expire: 1000 },
      { useBaseUrl: true }
    );
  }
  /**
   * 保存会话列表
   * @param {Object} data - 会话数据
   */
  async saveConversationItems(data) {
    return Restful.post('/conversations/items/save', data, {
      useConversationUrl: true,
      isIm: true,
    });
  }

  /**
   * 查询会话列表
   * @param {Object} data - 查询参数
   */
  async queryConversationItems(data) {
    return Restful.get('/conversations/items/query', data, {
      useConversationUrl: true,
      isIm: true,
    });
  }
  /**
   * 删除会话
   * @param {Object} data - 会话数据
   */
  async deleteConversationItems(data) {
    return Restful.post('/conversations/items/delete', data, {
      useConversationUrl: true,
      isIm: true,
    });
  }
}

export default new ImService();
