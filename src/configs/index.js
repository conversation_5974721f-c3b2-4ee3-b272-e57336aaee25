let env = 'dev';

if (__DEV__) {
  env = 'dev';
}

const defaultConfig = {
  env,
  printLog: __DEV__, // 是否打印日志
  printHttpResponse: __DEV__, // 是否打印Http Response
  printHttpResponseError: __DEV__, // 是否打印Http Response Error
  disableMessageRead: (isGroup) => isGroup, // 是否禁用消息已读功能
};

const appConfig = {
  // 用户的appkey
  // 用于在web demo中注册账号异步请求demo 服务器中使用
  dev: {
    appkey: '7c84850b694ca19d910ae4dbec1ccbb6',
    postUrl: 'https://apptest.netease.im',
  },
  prod: {
    appkey: '5777d6e721632dfd08bcf2c30296bf39',
    postUrl: 'https://app.netease.im',
  },
};

const base = {
  dev: {
    serverURL: 'http://47.243.46.222:30104/v1.0.0',
    // serverURL: 'http://192.168.31.182:8153/v1.0.0',
    im: {
      serverImURL: 'http://47.243.46.222:30109/1/api/v1', // 登录、注册等用户信息相关
      serverImConversationURL: 'http://47.243.46.222:30121/api/v1', // 会话数据
      serverBaseURL: 'http://47.243.46.222:30108/1/api/v1', // 好友相关地址等
      imWebSocketURL: 'ws://47.243.46.222:30108/1/api/v1/im/ws/', // imWebSocket
      // serverImURL: 'http://192.168.31.182:16666/1/api/v1', // 登录、注册等用户信息相关
      // serverBaseURL: 'http://192.168.31.182:11808/1/api/v1', // 好友相关地址等
      // imWebSocketURL: 'ws://192.168.31.182:11808/1/api/v1/im/ws/', // imWebSocket
      bigEmojiUrl:
        'https://kaixin-test.oss-cn-hongkong.aliyuncs.com/_publicfile/json/lottie/{{code}}.json',
      sensitiveWordsUrl:
        'https://kaixin-test.oss-cn-hongkong.aliyuncs.com/_publicfile/json/sensitive-words.json', // 敏感词
      complainUrl: 'http://47.243.46.222:30108/complaint/#/complaint', // 投诉地址
      appId: '5a8293684fde34f9',
      agoraAppId: '0fbea9eccec849c2b6f1b5d145f53c8c',
    },
    gpt: {
      serverURL: 'https://qa.mosainet.com/gpt-aws-api', // GPT机器人
    },
    // 本地消息显示数量，会影响性能，更多消息，需要单独做查看界面，用分页做
    localMsglimit: 20,
    // 简历分享出去的链接
    resumeShareURl: 'http://47.243.46.222:30104?lang=${lang}&token=${token}#/preview',
    // 帮助链接
    cmsHelpURl: 'http://47.243.46.222:30104/#/cms/${lang}/${id}',
    // 系统通知链接
    cmsNotifyURl: 'http://47.243.46.222:30104/#/notify/${id}',
    // 用户政策及协议
    agreementURl: 'http://47.243.46.222:30104/#/agreement/app',
    // 授权pc登录链接
    authorizedLoginURL: 'camhrpc.mosainet.com/saoLogin',
    registerUrl: 'http://47.243.46.222:30105/#/register',
    forgetPwdUrl: 'http://47.243.46.222:30105/#/Forgetpassword',
    longPayAppUrl: 'longpay://cam3spayqa.mosainet.com',
    appUrl: 'camhr://com.camhr.app',
    longPayDownloadUrl: 'https://cam3spayqa.mosainet.com/longpay/share/linking.html?ENV_CONFIG=qa',
    paywayPurchaseUrl:
      'https://checkout-sandbox.payway.com.kh/api/payment-gateway/v1/payments/purchase',
    iosStoreUrl: 'https://itunes.apple.com/cn/app/qq/id1442800498?mt=8',
    androidStoreUrl: 'https://play.google.com/store/apps/details?id=com.camhr.app',
    shareDownloadUrl: 'http://47.243.46.222:30105/linking.html?ENV_CONFIG=dev',
    smsCodeSalt: 't4nbzrFYp6hZFObGjJ6RSfPn6Bl97uXJYHFU4G5r2YqccfW7',
  },
  prod: {
    serverURL: 'https://api.camhr.com/v1.0.0',
    im: {
      serverImURL: 'https://api.camhr.com:16667/1/api/v1', // 登录、注册等用户信息相关
      serverImConversationURL: 'http://47.243.46.222:30121/api/v1', // 会话数据
      serverBaseURL: 'https://api.camhr.com:11809/1/api/v1', // 好友相关地址等
      imWebSocketURL: 'wss://api.camhr.com:11809/1/api/v1/im/ws/', // imWebSocket
      bigEmojiUrl:
        'https://camhr-prod.oss-cn-hongkong.aliyuncs.com/_publicfile/json/lottie/{{code}}.json',
      sensitiveWordsUrl:
        'https://camhr-prod.oss-cn-hongkong.aliyuncs.com/_publicfile/json/sensitive-words.json', // 敏感词
      complainUrl: 'https://api.camhr.com:11809/complaint/#/complaint', // 投诉地址
      appId: '5a8293684fde34f9',
      agoraAppId: 'd52d207047ce476eac7ee7c3565f7585',
    },
    gpt: {
      serverURL: 'https://api.camhr.com/gpt-aws-api',
    },
    // 本地消息显示数量，会影响性能，更多消息，需要单独做查看界面，用分页做
    localMsglimit: 20,
    // 简历分享出去的链接
    resumeShareURl: 'https://e.camhr.com?lang=${lang}&token=${token}#/preview',
    // 帮助链接
    cmsHelpURl: 'https://e.camhr.com/#/cms/${lang}/${id}',
    // 系统通知链接
    cmsNotifyURl: 'https://e.camhr.com/#/notify/${id}',
    // 用户政策及协议
    agreementURl: 'https://e.camhr.com/#/agreement/app',
    // 授权pc登录链接
    authorizedLoginURL: 'camhr.com/saoLogin',
    registerUrl: 'https://e.camhr.com/#/register',
    forgetPwdUrl: 'https://e.camhr.com/#/Forgetpassword',
    longPayAppUrl: 'longpay://slb.long-pay.net',
    appUrl: 'camhr://com.camhr.app',
    longPayDownloadUrl: 'https://slb.long-pay.net/longpay/share/linking.html',
    paywayPurchaseUrl: 'https://checkout.payway.com.kh/api/payment-gateway/v1/payments/purchase',
    iosStoreUrl: 'https://itunes.apple.com/cn/app/qq/id1442800498?mt=8',
    androidStoreUrl: 'https://play.google.com/store/apps/details?id=com.camhr.app',
    shareDownloadUrl: 'https://www.camhr.com/linking.html?ENV_CONFIG=prod',
    smsCodeSalt: 't4nbzrFYp6hZFObGjJ6RSfPn6Bl97uXJYHFU4G5r2YqccfW7',
  },
};

// 帮助界面文章配置，临时的
const aboutResume = {
  aboutResume: {
    en: [
      {
        id: 1,
        title: 'How to Write a Cover Letter for a Recruitment Consultant',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 1),
      },
      {
        id: 2,
        title: 'How to write a CV when you have no work experience',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 2),
      },
      {
        id: 3,
        title: 'How to Write a CV Career Summary',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 3),
      },
    ],
    zh: [
      {
        id: 1,
        title: '五大要素成就一份成功简历',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 1),
      },
      {
        id: 2,
        title: '外贸业务求职信范文',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 2),
      },
      {
        id: 3,
        title: '简历求职信怎么写',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 3),
      },
    ],
    km: [
      {
        id: 1,
        title: 'ចំនុចសំខាន់ៗដែលអ្នកគួរដឹងដើម្បីសរសេរ Cover letter មួយបានល្អនិងទាក់ទាញ',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 1),
      },
      {
        id: 2,
        title: 'បើគ្មានបទពិសោធន៍សោះតែម្ដង តើគួរសរសេរ CV ធ្វើម៉េចឱ្យទាក់ទាញ និងគេហៅសម្ភាសន៍?',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 2),
      },
      {
        id: 3,
        title: '៩ ចំណុចដែលអ្នកមិនត្រូវសរសេរនៅក្នុង CV របស់អ្នក',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 3),
      },
    ],
  },
};
const aboutInterview = {
  aboutInterview: {
    en: [
      {
        id: 4,
        title: 'How to Face an Interview',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 4),
      },
      {
        id: 5,
        title: 'How to Perform Well in a Group Interview',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 5),
      },
      {
        id: 6,
        title: 'How to Prepare for a Video Interview at Home',
        url: base[env].cmsHelpURl.replace('${lang}', 'en').replace('${id}', 6),
      },
    ],
    zh: [
      {
        id: 4,
        title: '七大征兆预示面试结果',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 4),
      },
      {
        id: 5,
        title: '你需要问HR什么问题?',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 5),
      },
      {
        id: 6,
        title: '电话面试技巧',
        url: base[env].cmsHelpURl.replace('${lang}', 'zh').replace('${id}', 6),
      },
    ],
    km: [
      {
        id: 4,
        title: '៩ចំណុចនេះ នឹងជួយឲ្យអ្នកជាប់ក្នុងការសម្ភាសន៍ដោយងាយ',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 4),
      },
      {
        id: 5,
        title: 'សំណួរអ្វីខ្លះដែលអ្នកត្រូវសួរដល់បុគ្គលិកធនធានមនុស្ស?',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 5),
      },
      {
        id: 6,
        title: 'តើធ្វើដូចម្តេចទើបរកការងារធ្វើបានលឿនទៅ?',
        url: base[env].cmsHelpURl.replace('${lang}', 'km').replace('${id}', 6),
      },
    ],
  },
};

export default Object.assign(defaultConfig, base[env], appConfig[env], aboutResume, aboutInterview);
