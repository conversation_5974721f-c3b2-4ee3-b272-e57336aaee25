import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { <PERSON><PERSON>, Header } from 'react-native-elements';
import ActionSheet from 'react-native-actionsheet';
import {
  Text,
  View,
  TouchableHighlight,
  Alert,
  FlatList,
  ScrollView,
  RefreshControl,
  Keyboard,
  Modal,
} from 'react-native';
import { Clipboard } from '../components';
import Hyperlink from 'react-native-hyperlink';
import {
  dynamicDetailStyle,
  bgColor,
  headerStyle,
  titleColor,
  baseBlueColor,
  desColor,
  subTitleColor,
  dynamicStyle,
  baseRedColor,
} from '../themes';
import { getAvatarSource } from '../res';
import util from '../util';
import I18n from '../i18n';
import Session from '../api/session';
import GoBack from '../components/goback';
import LoadingModal from '../components/loadingModal';
import InputBox from './dynamicComInputBox';
import Image from '../components/image';

@inject('dynamicAction', 'dynamicStore', 'userAction', 'personStore')
@observer
export default class dynamicDetail extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMoreComments = false;
    this.isDeleteDynamic = false;
    this.state = {
      commentsPage: 1,
      hasMoreComment: false,
      commentRefresh: false,
      selectComment: null,
      showLoading: true,
      placeholderComment: I18n.t('page_comment_ph_comment'),
      keyboardShow: false,
      replyCommentId: 0,
      isLiked: false,
      likes: 0,
      info: null,
      loading: false,
    };
  }

  componentDidMount() {
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
    this.onGetDetail();
  }

  componentWillUnmount() {
    this.goBack();
    this.props.dynamicAction.resetComments();
  }

  onGetDetail = () => {
    const { info } = this.props.navigation.state.params;
    this.props.userAction.getTwitterDetail(info.id).then(
      (ress) => {
        this.setState({ info: ress });
        if (ress && ress.id) {
          this.setState({ isLiked: ress.liked, likes: ress.likes });
          this.getComments();
        } else {
          this.setState({ showLoading: false });
        }
      },
      () => {
        this.setState({ showLoading: false });
      }
    );
  };

  onCommentRefresh = async () => {
    const temp = this.props.navigation.state.params.info;
    this.props.userAction.getTwitterDetail(temp.id).then((ress) => {
      this.setState({ info: ress });
      if (ress && ress.id) {
        this.setState({ isLiked: ress.liked, likes: ress.likes });
      }
    });
    const { info } = this.state;
    if (info && info.id) {
      this.setState({ commentRefresh: true });
      this.state.page = 1;
      await this.getComments();
    }
  };

  onLoadMoreComments = async () => {
    const { info } = this.state;
    const { commentList } = this.props.dynamicStore;
    if (
      this.state.hasMoreComment &&
      !this.isLoadingMoreComments &&
      info &&
      info.id &&
      commentList &&
      commentList.length > 0
    ) {
      this.state.commentsPage += 1;
      this.isLoadingMoreComments = true;
      await this.getComments();
      setTimeout(() => {
        this.isLoadingMoreComments = false;
      }, 0);
    }
  };

  onCommentClick = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.setState({ selectComment: item });
        if (item.mine) {
          this.setState({ keyboardShow: false, replyCommentId: 0 });
          this.commentActionSheet.show();
        } else {
          this.setState({ replyCommentId: item.id });
          this.setState({
            placeholderComment: `${I18n.t('page_dynamic_reply_text')}${
              item.userName ? item.userName : I18n.t('page_dynamic_title_ay_username')
            }:`,
            keyboardShow: true,
          });
        }
      }
    });
  };

  onActionSheetSelect = (index) => {
    if (index === 0) {
      this.onDeleteComment();
    }
  };

  onDeleteComment = async () => {
    const { commentList } = this.props.dynamicStore;
    const data = this.state.selectComment;
    const ress = await this.props.dynamicAction.deleteComment(data.twitterId, data.id);
    if (ress) {
      if (ress.message) {
        toast.show(ress.message);
      }
      if (ress.successful) {
        const deleteIndex = commentList.findIndex((x) => x.id === data.id);
        commentList.splice(deleteIndex, 1);
        this.props.personStore.commentList = commentList;
        this.getComments();
      }
    }
  };

  onDeleteDynamic = (info) => {
    Alert.alert(I18n.t('page_dynamic_delete_tips'), '', [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: async () => {
          try {
            const ress = await this.props.dynamicAction.deleteDynamic(info.id);
            if (ress) {
              if (ress.message) {
                toast.show(ress.message);
              }
              if (ress.successful) {
                this.isDeleteDynamic = true;
                const fuc = this.props.navigation.state.params;
                if (fuc && fuc.onDynamicDelete) {
                  fuc.onDynamicDelete(info.id);
                }
                this.props.dynamicAction.resetComments();
                const { navigation } = this.props;
                navigation.goBack();
              }
            }
          } catch (error) {
            console.log(error);
          }
        },
      },
    ]);
  };

  onAvatarsClick = () => {
    const { info } = this.state;
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        if (info.mine) return;
        navigation.navigate('userProfile', {
          data: info,
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  onToUserProfile = (item) => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        if (item.mine) return;
        navigation.push('userProfile', {
          data: item,
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onDynamicCopySheetSelect = (index) => {
    if (index === 0) {
      const { info } = this.state;
      Clipboard.setString((info && info.content) || '');
      global.toast.show(I18n.t('page_dynamic_text_copy_success'));
    }
  };

  onCommentCopySheetSelect = (index) => {
    if (index === 0) {
      const { selectComment } = this.state;
      Clipboard.setString((selectComment && selectComment.content) || '');
      global.toast.show(I18n.t('page_dynamic_text_copy_success'));
    }
  };

  getComments = async () => {
    const { info } = this.props.navigation.state.params;
    try {
      await this.props.dynamicAction.queryComments(info.id, {
        page: this.state.commentsPage,
        size: 10,
      });
      const { commentTotalCount, commentList } = this.props.dynamicStore;
      this.setState({
        hasMoreComment: commentList && commentList.length < parseFloat(commentTotalCount),
        commentRefresh: false,
        showLoading: false,
      });
    } catch (error) {
      this.setState({ commentRefresh: false, showLoading: false });
    }
  };

  _keyboardDidHide = () => {
    this.setState({
      placeholderComment: I18n.t('page_comment_ph_comment'),
      keyboardShow: false,
      replyCommentId: 0,
    });
  };

  goBack = async () => {
    if (this.isDeleteDynamic) {
      return;
    }
    const { dynamicId } = this.props.navigation.state.params;
    const { info } = this.state;
    const {
      dynamicStore: { commentTotalCount },
      navigation,
    } = this.props;
    const { likes, isLiked } = this.state;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onDynamicDelete && info && !info.id) {
      await fuc.onDynamicDelete(dynamicId);
      navigation.goBack();
      return;
    }
    if (fuc && fuc.onRefreshDynamic && info && info.id) {
      await fuc.onRefreshDynamic(info.id, commentTotalCount, likes, isLiked);
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  previewDetail = (i) => {
    const { info } = this.state;
    const { navigation } = this.props;
    const imgArr = [];
    info.images.forEach((item) => {
      imgArr.push({ url: item });
    });
    navigation.navigate('previewImage', { images: imgArr, index: i });
  };

  toggleInputModal = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.setState({ keyboardShow: !this.state.keyboardShow });
      } else {
        this.onShowAlert();
      }
    });
  };

  hideInputBox = async (result) => {
    toast.show(result && result.message ? result.message : I18n.t('page_comment_create_fail'));
    if (result && result.successful) {
      this.setState({ keyboardShow: false });
    }
    this.setState({ commentsPage: 1, replyCommentId: 0, placeholderComment: '' });
    await this.getComments(this.state.twitterId);
  };

  changeFavorite = (item) => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        if (this.state.isLiked) {
          const ress = await this.props.dynamicAction.dislikes(item.id);
          if (ress && ress.successful) {
            this.setState({ isLiked: false });
          }
          toast.show(ress.message);
          this.setState({
            likes: parseInt(this.state.likes, 10) > 0 ? parseInt(this.state.likes, 10) - 1 : 0,
          });
        } else {
          const ress = await this.props.dynamicAction.likes(item.id);
          if (ress && ress.successful) {
            this.setState({ isLiked: true });
          }
          toast.show(ress.message);
          this.setState({ likes: parseInt(this.state.likes, 10) + 1 });
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  renderCommentItemView({ item }) {
    return (
      // eslint-disable-next-line react/jsx-no-bind
      <TouchableHighlight
        underlayColor="transparent"
        key={item.id}
        onPress={() => this.onCommentClick(item)}
      >
        <View style={dynamicDetailStyle.commentItemtContent}>
          <View style={dynamicDetailStyle.commentItemLeft}>
            <TouchableHighlight
              underlayColor="transparent"
              onPress={() => this.onToUserProfile(item)}
            >
              <Image source={getAvatarSource(item.avatar)} style={dynamicDetailStyle.commentIcon} />
            </TouchableHighlight>
          </View>
          <View style={[dynamicDetailStyle.commentItemRight, { flexShrink: 100 }]}>
            <View style={dynamicDetailStyle.commentItemtTitleContent}>
              <Text style={dynamicDetailStyle.commentName}>
                {item.userName ? item.userName : I18n.t('page_dynamic_title_ay_username')}
              </Text>
              <Text style={dynamicDetailStyle.commentTime}>
                {item.createAt_unixtime ? util.getDiffBetweenHanSimple(item.createAt_unixtime) : ''}
              </Text>
            </View>
            <TouchableHighlight
              underlayColor="#f2f2f2"
              onPress={() => this.onCommentClick(item)}
              onLongPress={() => {
                this.setState({ selectComment: item });
                this.copyCommentActionSheet.show();
              }}
            >
              {parseInt(item.replyCommentId, 10) !== 0 ? (
                <View style={dynamicDetailStyle.commentContent}>
                  <Hyperlink
                    onPress={(url, text) => {
                      if (!url.startsWith('http')) {
                        return;
                      }
                      const { navigation } = this.props;
                      navigation.navigate('previewWeb', { title: url, url });
                    }}
                    linkStyle={{ color: '#2980b9' }}
                  >
                    <Text style={{ color: titleColor }}>
                      {`${I18n.t('page_dynamic_reply_text')} `}
                      <Text style={{ color: baseBlueColor }}>
                        {item.replyUserName
                          ? item.replyUserName
                          : I18n.t('page_dynamic_title_ay_username')}
                      </Text>
                      <Text style={dynamicDetailStyle.commentContent}>{`: ${item.content}`}</Text>
                    </Text>
                  </Hyperlink>
                </View>
              ) : (
                <Hyperlink
                  onPress={(url, text) => {
                    if (!url.startsWith('http')) {
                      return;
                    }
                    const { navigation } = this.props;
                    navigation.navigate('previewWeb', { title: url, url });
                  }}
                  linkStyle={{ color: '#2980b9' }}
                >
                  <Text style={dynamicDetailStyle.commentContent}>{item.content}</Text>
                </Hyperlink>
              )}
            </TouchableHighlight>
          </View>
        </View>
      </TouchableHighlight>
    );
  }

  renderNoComments = () => {
    if (this.state.showLoading) {
      return <Text />;
    }
    return (
      <Text style={[dynamicStyle.noComments, { color: subTitleColor }]}>
        {I18n.t('page_dynamic_no_comment')}
      </Text>
    );
  };

  renderDeleteCommponet = () => {
    if (!this.state.showLoading) {
      return (
        <View style={[dynamicDetailStyle.top, { marginRight: 15 }]}>
          <TouchableHighlight underlayColor="transparent">
            <Image
              source={getAvatarSource('')}
              style={[dynamicDetailStyle.icon, { backgroundColor: '#fff' }]}
            />
          </TouchableHighlight>
          <View style={[dynamicDetailStyle.rightContent, { marginRight: 15 }]}>
            <Text style={dynamicDetailStyle.title}>{I18n.t('page_dynamic_title_ay_username')}</Text>
            <View style={dynamicDetailStyle.deleteTextContainer}>
              <Text style={dynamicDetailStyle.deleteText}>
                {I18n.t('page_dynamic_has_been_deleted_text')}
              </Text>
            </View>
          </View>
        </View>
      );
    }
    return <View />;
  };

  render() {
    const { info } = this.state;
    const {
      dynamicStore: { commentList, commentTotalCount },
      navigation,
    } = this.props;
    const {
      commentRefresh,
      likes,
      isLiked,
      keyboardShow,
      replyCommentId,
      placeholderComment,
      showLoading,
      loading,
    } = this.state;
    return (
      <View style={dynamicDetailStyle.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_dynamic_header_detail_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView
          style={{ backgroundColor: '#fff' }}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={200}
          // eslint-disable-next-line react/jsx-no-bind
          onScroll={this.onLoadMoreComments.bind(this)}
          refreshControl={
            <RefreshControl refreshing={commentRefresh} onRefresh={this.onCommentRefresh} />
          }
        >
          {info && info.id && !this.state.showLoading ? (
            <View style={dynamicDetailStyle.top}>
              <TouchableHighlight
                underlayColor="transparent"
                onPress={() => {
                  this.onAvatarsClick();
                }}
              >
                <Image
                  source={getAvatarSource(info.avatar)}
                  style={[dynamicDetailStyle.icon, { backgroundColor: '#fff' }]}
                />
              </TouchableHighlight>
              <View style={dynamicDetailStyle.rightContent}>
                <Text style={dynamicDetailStyle.title}>
                  {info.userName ? info.userName : I18n.t('page_dynamic_title_ay_username')}
                </Text>
                <TouchableHighlight
                  underlayColor="#f2f2f2"
                  onLongPress={() => {
                    this.copyDynamicActionSheet.show();
                  }}
                >
                  <View
                    style={{
                      marginHorizontal: 15,
                      marginRight: 25,
                      paddingRight: 25,
                    }}
                  >
                    <Hyperlink
                      onPress={(url, text) => {
                        if (!url.startsWith('http')) {
                          return;
                        }
                        navigation.navigate('previewWeb', { title: url, url });
                      }}
                      linkStyle={{ color: '#2980b9' }}
                    >
                      <Text style={dynamicDetailStyle.content}>{info.content}</Text>
                    </Hyperlink>
                  </View>
                </TouchableHighlight>
                <View style={[dynamicDetailStyle.imagesContent, { marginBottom: 10 }]}>
                  {info.images.map((item, i) => (
                    <TouchableHighlight
                      underlayColor="transparent"
                      key={`${i + 1}`}
                      onPress={() => {
                        this.previewDetail(i);
                      }}
                    >
                      <Image source={{ uri: item }} style={dynamicDetailStyle.image} />
                    </TouchableHighlight>
                  ))}
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                  }}
                >
                  <Text numberOfLines={1} style={dynamicDetailStyle.time}>
                    {info.createAt_unixtime ? util.getDiffBetweenHan(info.createAt_unixtime) : ''}
                  </Text>
                  {info.mine ? (
                    <TouchableHighlight
                      underlayColor="transparent"
                      onPress={() => {
                        this.onDeleteDynamic(info);
                      }}
                    >
                      <Text style={{ marginLeft: 10, fontSize: 14, color: baseBlueColor }}>
                        {I18n.t('page_dynamic_detail_delete')}
                      </Text>
                    </TouchableHighlight>
                  ) : (
                    <Text style={{ display: 'none' }} />
                  )}
                </View>
              </View>
            </View>
          ) : (
            this.renderDeleteCommponet()
          )}
          <View
            style={{ height: 12, backgroundColor: !this.state.showLoading ? bgColor : '#fff' }}
          />
          {!this.state.showLoading ? (
            <View style={dynamicDetailStyle.likeContainer}>
              <View style={dynamicDetailStyle.commentCon}>
                <Icon name="chatbox-outline" type="ionicon" size={20} color={desColor} />
                {I18n.locale === 'zh' ? (
                  <Text style={dynamicDetailStyle.likeConText}>
                    {I18n.t('page_comment_text_title')}
                    {info && info.id ? commentTotalCount : 0}
                    {I18n.t('page_comment_title_strip')}
                  </Text>
                ) : (
                  <Text style={dynamicDetailStyle.likeConText}>
                    {I18n.t('page_comment_text_title')} {info && info.id ? commentTotalCount : 0}{' '}
                    {I18n.t('page_comment_title_strip')}
                  </Text>
                )}
              </View>
              <View style={dynamicDetailStyle.commentCon}>
                {I18n.locale === 'zh' ? (
                  <Text style={dynamicDetailStyle.likeConText}>
                    {info && info.id ? likes : 0}
                    {I18n.t('page_dynamic_person_liked_text')}
                  </Text>
                ) : (
                  <Text style={dynamicDetailStyle.likeConText}>
                    {info && info.id ? likes : 0} {I18n.t('page_dynamic_person_liked_text')}
                  </Text>
                )}
                <Icon
                  onPress={() =>
                    util.HandlerOnceTap(() => {
                      if (info && info.id) {
                        this.changeFavorite(info);
                      }
                    })
                  }
                  name={info && info.id && isLiked ? 'heart' : 'heart-outlined'}
                  type="entypo"
                  size={20}
                  color={info && info.id && isLiked ? baseRedColor : desColor}
                  iconStyle={{ marginLeft: 4 }}
                />
              </View>
            </View>
          ) : (
            <View />
          )}
          <View
            style={{ height: 6, backgroundColor: !this.state.showLoading ? bgColor : '#fff' }}
          />
          {info && info.id && commentList && commentList.length > 0 ? (
            <View style={{ backgroundColor: '#fff' }}>
              <FlatList
                data={commentList}
                // eslint-disable-next-line react/jsx-no-bind
                renderItem={this.renderCommentItemView.bind(this)}
                keyExtractor={(item, index) => index + item}
                showsVerticalScrollIndicator={false}
                horizontal={false}
                // ListFooterComponent={this.renderCommentFooter}
                ItemSeparatorComponent={() => (
                  <View style={{ height: 1, backgroundColor: bgColor, marginHorizontal: 15 }} />
                )}
              />
            </View>
          ) : (
            this.renderNoComments()
          )}
        </ScrollView>
        <Modal
          onRequestClose={() => this.setState({ keyboardShow: false })}
          animationType="none"
          presentationStyle="overFullScreen"
          transparent
          visible={keyboardShow}
        >
          <View style={dynamicStyle.commentBackGround}>
            <View style={dynamicStyle.inputBackTop}>
              <TouchableHighlight underlayColor="transparent" onPress={this.toggleInputModal}>
                <Text style={dynamicStyle.commentBackTopEmpty} />
              </TouchableHighlight>
            </View>
            <InputBox
              onPress={this.hideInputBox}
              info={info}
              ref={(ref) => {
                this.input = ref;
              }}
              navigation={this.props.navigation}
              replyCommentId={replyCommentId}
              placeholderComment={placeholderComment}
              showLoading={() => this.setState({ loading: true, keyboardShow: false })}
              hideLoading={() => this.setState({ loading: false })}
            />
          </View>
        </Modal>
        {info && info.id && !this.state.showLoading ? (
          <View
            style={[
              dynamicStyle.commentInputBack,
              { backgroundColor: '#fff', borderTopColor: bgColor, borderTopWidth: 1 },
            ]}
          >
            <TouchableHighlight underlayColor="transparent" onPress={this.toggleInputModal}>
              <View
                style={[
                  dynamicStyle.commentInputView,
                  { backgroundColor: bgColor, borderRadius: 4 },
                ]}
              >
                <Text style={[dynamicStyle.commentInputText, { paddingLeft: 6 }]}>
                  {I18n.t('page_comment_ph_comment')}
                </Text>
              </View>
            </TouchableHighlight>
          </View>
        ) : (
          <Text />
        )}
        <ActionSheet
          ref={(ref) => {
            this.commentActionSheet = ref;
          }}
          title={I18n.t('page_dynamic_delete_comment')}
          options={[I18n.t('page_resume_btn_del'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onActionSheetSelect}
        />
        <ActionSheet
          ref={(ref) => {
            this.copyDynamicActionSheet = ref;
          }}
          options={[I18n.t('page_dynamic_text_copy'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onDynamicCopySheetSelect}
        />
        <ActionSheet
          ref={(ref) => {
            this.copyCommentActionSheet = ref;
          }}
          options={[I18n.t('page_dynamic_text_copy'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          onPress={this.onCommentCopySheetSelect}
        />
        <LoadingModal isOpen={showLoading || loading} loadingTips={false} />
      </View>
    );
  }
}
