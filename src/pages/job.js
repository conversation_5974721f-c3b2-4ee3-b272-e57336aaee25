import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  Text,
  View,
  TouchableOpacity,
  SectionList,
  Modal,
  TouchableHighlight,
  ScrollView,
  InteractionManager,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SplashScreen from 'react-native-splash-screen';
import { Input, Header, Icon, Divider, Badge, ListItem } from 'react-native-elements';
import {
  headerStyle,
  baseBlueColor,
  titleColor,
  desColor,
  jobStyle,
  subTitleColor,
} from '../themes';
import { RVW } from '../common';
import I18n from '../i18n';
import res from '../res';
import Session from '../api/session';
import MySwiper from './swiperItem';
import GuideModal from '../components/guideModal';
import CheckVersionModal from '../components/checkVersionModal';
import LocationModal from '../components/locationModal';
import util from '../util';
import CheckFlow, {
  EVENT_NO_INTENSION,
  EVENT_NO_RESUME,
  EVENT_NO_BIND_MOBILE,
  EVENT_LOCATION_DEFAULT,
  EVENT_LOCATION_CHANGE,
  EVENT_LOCATION_FAIL,
} from '../store/actions/flow';
import Image from '../components/image';
import constant from '../store/constant';
import JobItem from '../components/listItem/jobItem';
import BindPhoneModal from '../components/bindPhoneModal';
import NavigationService from '../navigationService';

@inject(
  'jobStore',
  'jobAction',
  'resumeStore',
  'resumeAction',
  'cityAction',
  'userAction',
  'personStore',
  'globalStore',
  'applicationAction',
  'globalAction'
)
@observer
export default class Job extends Component {
  static navigationOptions = {
    gestureEnabled: false,
  };

  constructor(props) {
    super(props);
    this.isLoadingMore = false;
    this.page = 1;
    this.state = {
      showType: 0,
      isModalVisible: false,
      showLocationModal: false,
      isSwitchCity: false,
      confirmTitle: '',
      hasMore: false,
      refreshing: false,
      cityData: {}, // 当前城市信息
      localCityData: {}, // 定位的城市信息
      showLoading: false,
      comFilterNumber: 0,
      reqFilterNumber: 0,
      companyShow: false,
      requireShow: false,
      urgentShow: false,
      isUrgent: false,
      recommendation: false,
      isNearby: false,
      isZh: false,
      isChina: false,
      workYearMin: null,
      workYearMax: null,
      educationIds: [],
      teamScaleIds: [],
      industyIds: [],
      workYearList: [
        {
          name: '0-1',
          min: 0,
          max: 1,
          isSelected: false,
          tempSelected: false,
        },
        {
          name: '1-3',
          min: 1,
          max: 3,
          isSelected: false,
          tempSelected: false,
        },
        {
          name: '3-5',
          min: 3,
          max: 5,
          isSelected: false,
          tempSelected: false,
        },
        {
          name: '5-10',
          min: 5,
          max: 10,
          isSelected: false,
          tempSelected: false,
        },
        {
          name: '10',
          min: 10,
          isSelected: false,
          tempSelected: false,
        },
      ],
      jobTypeLabel: '',
      reqJobTitle: null,
      jobTypeList: [],
    };
    this.init();
  }

  init() {
    SplashScreen.hide(); // 关闭启动屏幕
    if (this.props.globalStore.isEnterprise) return;
    if (Platform.OS.toLowerCase() === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }
    const {
      personStore: { isLogin },
    } = this.props;
    // modify by moke
    CheckFlow.init();
    CheckFlow.on(EVENT_LOCATION_DEFAULT, this.onDefaultLocation);
    CheckFlow.on(EVENT_LOCATION_CHANGE, this.onChangeLocation);
    CheckFlow.on(EVENT_LOCATION_FAIL, this.onFailLocation);
    Session.isLogin().then((hasLogin) => {
      if (hasLogin || isLogin) {
        CheckFlow.on(EVENT_NO_INTENSION, this.onNoIntension);
        CheckFlow.on(EVENT_NO_RESUME, this.onNoResume);
        CheckFlow.on(EVENT_NO_BIND_MOBILE, this.onNoBindMobile);

        CheckFlow.start();
      } else {
        CheckFlow.continue(4);
      }
    });
  }

  async componentDidMount() {
    if (this.props.globalStore.isEnterprise) return;

    this.initState();
    await this.initJobList();
    this.getHasIntention();
    global.emitter.on('languageChange', this.onChangeLanguage);
    global.emitter.on('reloadData', this.onReloadData);
    global.emitter.on('closeModal', this.onCloseModal);
    global.emitter.on('refreshList', this.onRefreshList);
    global.emitter.on('resetStatus', this.onResetStatus);
    global.emitter.on('switchCity', this.saveArea);
    await InteractionManager.runAfterInteractions(() => {
      this.isLogin();
    });
  }

  componentWillUnmount() {
    global.emitter.off('languageChange', this.onChangeLanguage);
    global.emitter.off('reloadData', this.onReloadData);
    global.emitter.off('closeModal', this.onCloseModal);
    global.emitter.off('refreshList', this.onRefreshList);
    global.emitter.off('resetStatus', this.onResetStatus);
    global.emitter.off('switchCity', this.saveArea);
    AsyncStorage.removeItem('hasShowIndictor');
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  getHasIntention = async () => {
    try {
      const hasIntension = await this.props.applicationAction.checkIntension();
      if (!hasIntension && this.props.globalStore.remindCompleteIntension) {
        this.props.globalAction.setRemindCompleteIntension(false);
        NavigationService.navigate('jobIntensionComplete');
      }
    } catch (error) {}
  };

  onChangeLanguage = () => {
    this.initState();
    this.isLogin();
    this.page = 1;
    this.getData();
    const { cityData } = this.state;
    const newCityData = cityData;
    newCityData.cityName = this.getCityName(cityData);
    this.setState({ cityData: newCityData });
    this.props.jobAction.saveCurrentCity(newCityData);
  };

  onReloadData = () => {
    this.initState();
  };

  onCloseModal = () => {
    this.setState({
      isModalVisible: false,
      showLocationModal: false,
    });
  };

  onRefreshList = () => {
    this.page = 1;
    this.getData();
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        this.props.jobAction.getJobStatistics();
        this.props.userAction.getCurrUserInfo();
        this.props.resumeAction.getResumes();
      }
    });
    this.isLogin();
  };

  onResetStatus = () => {
    this.setState({ showLoading: false, refreshing: false });
  };

  onNoIntension = () => {
    // this.setState({ isModalVisible: true, showType: 1 });
  };

  onNoResume = () => {
    this.setState({ isModalVisible: true, showType: 2 });
  };

  onNoBindMobile = () => {
    // this.setState({ isModalVisible: true, showType: 3 });
  };

  onDefaultLocation = (location) => {
    const newCityData = location;
    newCityData.cityName = this.getCityName(location);

    this.setState({ cityData: newCityData });
    this.props.jobAction.saveCurrentCity(newCityData);
    const data = location;
    data.page = 1;
    this.getJobs(data);
  };

  onChangeLocation = (location) => {
    this.setState({
      isSwitchCity: true,
      localCityData: location,
      showLocationModal: true,
      confirmTitle: I18n.t('page_get_location_to_change'),
    });
  };

  onFailLocation = () => {
    this.getLocationFailure();
  };

  onCheckIntensions = () => {
    const {
      resumeStore: { resumeList },
    } = this.props;
    const { cityData } = this.state;
    if (resumeList.length === 0) {
      const data = { page: 1 };
      AsyncStorage.getItem('hasShowIndictor').then((reslut) => {
        if (reslut) {
          AsyncStorage.removeItem('hasShowIndictor');
        } else {
          AsyncStorage.setItem('hasShowIndictor', 'hasShowIndictor');
          this.setState({ isModalVisible: true });
        }
      });
      if (cityData.locationId) {
        data.locationId = cityData.locationId;
      }
      if (cityData.placeId) {
        data.addressCode = cityData.threeLevelCityData
          ? cityData.threeLevelCityData.placeId
          : cityData.placeId;
        data.location = cityData.location;
      }
      this.getJobs(data);
    } else {
      if (
        resumeList[0].profile &&
        resumeList[0].profile.firstName &&
        resumeList[0].profile.mobile
      ) {
        this.setState({ isModalVisible: false });
      } else {
        this.setState({ isModalVisible: true });
      }
      const data = { page: 1 };
      if (cityData.locationId) {
        data.locationId = cityData.locationId;
      }
      if (cityData.placeId) {
        data.addressCode = cityData.placeId;
        data.location = cityData.location;
      }
      this.getJobs(data);
    }
  };

  onBackAndroid = () => {
    if (this.props.navigation.state.routeName !== 'JobTab') {
      // this.props.navigation.pop();
    }
    // 禁用返回键
    if (this.props.navigation.isFocused()) {
      // 判断  该页面是否处于聚焦状态
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp(); // 直接退出APP
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT, ToastAndroid.CENTER);
      return true;
    }
    // 回调函数onBackAndroid中的return true是必不可少的 --- 大坑，信你个鬼， 必须为false，不然会有bug
    return false;
  };

  onRefresh = async () => {
    this.setState({ refreshing: true });
    this.props.userAction.getBanners();
    this.page = 1;
    await this.getData();
    this.setState({ refreshing: false });
  };

  onLoadMore = async () => {
    if (this.state.hasMore && !this.isLoadingMore) {
      this.page += 1;
      this.isLoadingMore = true;
      await this.getData();
      setTimeout(() => {
        this.isLoadingMore = false;
      }, 0);
    }
  };

  /**
   * 处理事件
   */
  onToJobDetail = async (item) => {
    const isLogin = await Session.isLogin();
    const {
      personStore: { me },
    } = this.props;
    if (isLogin && !me?.mobile) {
      this.bindPhoneModal.wrappedInstance.show();
      return;
    }
    this.props.navigation.navigate('jobDetail', { detail: item });
  };

  onSearch = async () => {
    const isLogin = await Session.isLogin();
    const {
      personStore: { me },
    } = this.props;
    if (isLogin && !me?.mobile) {
      this.bindPhoneModal.wrappedInstance.show();
      return;
    }
    this.onCancelTap();
    this.props.navigation.navigate('jobSearch', {
      onSelect: (item) => {
        this.selectedCity(item);
      },
    });
  };

  onFilterViewTap = () => {
    const {
      jobStore: { jobList },
    } = this.props;
    if (
      !this.state.showLoading &&
      jobList.length &&
      jobList[0] &&
      jobList[0].data &&
      jobList[0].data.length === 0
    ) {
      return;
    }
    this.onToTargetIndex.scrollToLocation({
      sectionIndex: 0,
      itemIndex: 0,
      viewOffset: 40,
      animated: false,
    });
  };

  onJobFilter = () => {
    this.onFilterViewTap();
    InteractionManager.runAfterInteractions(() => {
      this.setState({ urgentShow: !this.state.urgentShow, companyShow: false, requireShow: false });
    });
  };

  onCompanyFilter = () => {
    this.onFilterViewTap();
    this.props.jobStore.teamScaleList = this.onDataFilterInit(this.props.jobStore.teamScaleList);
    this.props.jobStore.indutrialList = this.onDataFilterInit(this.props.jobStore.indutrialList);
    InteractionManager.runAfterInteractions(() => {
      this.setState({
        companyShow: !this.state.companyShow,
        requireShow: false,
        urgentShow: false,
      });
    });
  };

  onRequireFilter = () => {
    this.onFilterViewTap();
    this.props.jobStore.qualificationList = this.onDataFilterInit(
      this.props.jobStore.qualificationList
    );
    this.state.workYearList = this.onDataFilterInit(this.state.workYearList);
    InteractionManager.runAfterInteractions(() => {
      this.setState({
        companyShow: false,
        requireShow: !this.state.requireShow,
        urgentShow: false,
      });
    });
  };

  onDataFilterInit = (array) => {
    const finalArr = this.state.companyShow
      ? array.map((item) => {
          item.tempSelected = false;
          return item;
        })
      : array.map((item) => {
          item.tempSelected = item.isSelected;
          return item;
        });
    return finalArr;
  };

  onExperienceBadgeClick = (item) => {
    const tempArr = this.state.workYearList.map((l) => {
      l.tempSelected = false;
      return l;
    });
    item.tempSelected = !item.tempSelected;
    this.setState({
      workYearList: tempArr,
    });
  };

  onBadgeClick = (item) => {
    requestAnimationFrame(() => {
      item.tempSelected = !item.tempSelected;
    });
  };

  onIntensionBadgeClick = (item) => {
    if (this.state.isUrgent || this.state.recommendation || this.state.isNearby) {
      const {
        isUrgent,
        recommendation,
        isNearby,
        isZh,
        isChina,
        educationIds,
        workYearMax,
        workYearMin,
        teamScaleIds,
        industyIds,
        cityData,
      } = this.state;
      let newJobTitle = '';

      if (this.props.resumeStore && this.props.resumeStore.resumeList) {
        if (item.isSelected) {
          this.props.resumeStore.resumeList = this.props.resumeStore.resumeList.map((x) => {
            x.isSelected = false;
            return x;
          });
          this.setState({ reqJobTitle: '' });
          newJobTitle = '';
        } else {
          this.props.resumeStore.resumeList = this.props.resumeStore.resumeList.map((x) => {
            x.isSelected = x.resumeId === item.resumeId;
            return x;
          });
          this.setState({
            reqJobTitle: item.intention.reqJobTitle,
          });
          newJobTitle = item.intention.reqJobTitle;
        }
      }

      const data = {
        page: 1,
        employerScaleIds: teamScaleIds,
        industrialIds: industyIds,
        qualificationIds: educationIds,
      };

      if (isUrgent) data.urgent = isUrgent;
      if (recommendation) data.recommendation = recommendation;
      if (isZh) data.language = 'zh';
      if (isChina) data.chinaCommerce = true;
      if (workYearMax) data.workYearMax = workYearMax;
      if (workYearMin) data.workYearMin = workYearMin;
      if (cityData.locationId) data.locationId = cityData.locationId;
      if (cityData.placeId) {
        data.addressCode = cityData.placeId;
        data.location = cityData.location;
      }
      if (newJobTitle) {
        data.jobTitle = item.intention.reqJobTitle;
      }
      this.getJobs(data);
    }
  };

  onCancelTap = () => {
    this.setState({ companyShow: false, requireShow: false, urgentShow: false });
  };

  onResetTap = (str) => {
    if (str === 'require') {
      const { workYearList } = this.state;
      this.props.jobStore.qualificationList = this.resetParam(
        this.props.jobStore.qualificationList
      );
      this.setState({
        workYearMax: null,
        workYearMin: null,
        workYearList: this.resetParam(workYearList),
      });
    } else {
      this.props.jobStore.teamScaleList = this.resetParam(this.props.jobStore.teamScaleList);
      this.props.jobStore.indutrialList = this.resetParam(this.props.jobStore.indutrialList);
    }
  };

  onConfirmTap = (str) => {
    this.onCancelTap();
    if (str === 'require') {
      this.getRequireFilterData();
    } else {
      this.getFilterCompanyData();
    }
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onSelectIntension = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const {
          personStore: { me },
        } = this.props;
        if (!me?.mobile) {
          this.bindPhoneModal.wrappedInstance.show();
          return;
        }
        const { navigation } = this.props;
        navigation.navigate('jobIntention', {
          onShowModal: () => {
            CheckFlow.continue();
          },
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  onSelectUrgent = async (item) => {
    const { jobTypeList } = this.state;
    const tempArr = jobTypeList.map((l) => {
      l.isSelected = false;
      return l;
    });
    item.isSelected = !item.isSelected;
    this.onCancelTap();
    this.setState({
      isUrgent: item.type === 'urgent',
      recommendation: item.type === 'recommend',
      isNearby: item.type === 'nearby',
      isZh: item.type === 'zh',
      isChina: item.type === 'china',
      urgentShow: false,
      jobTypeLabel: item.label,
      jobTypeList: tempArr,
    });
    console.log('111112', tempArr);
    const {
      teamScaleIds,
      industyIds,
      educationIds,
      workYearMax,
      workYearMin,
      reqJobTitle,
      cityData,
    } = this.state;

    const data = {
      page: 1,
      employerScaleIds: teamScaleIds,
      industrialIds: industyIds,
      qualificationIds: educationIds,
    };
    if (item.type === 'urgent') {
      data.urgent = true;
    }
    if (item.type === 'recommend') {
      data.recommendation = true;
    }
    if (item.type === 'nearby') {
      data.nearby = true;
    }
    if (item.type === 'china') {
      data.chinaCommerce = true;
    }
    if (item.type === 'zh') {
      data.language = 'zh';
    }
    if (workYearMax) {
      data.workYearMax = workYearMax;
    }
    if (workYearMin) {
      data.workYearMin = workYearMin;
    }
    if (cityData.locationId) {
      data.locationId = cityData.locationId;
    }
    if (cityData.placeId) {
      data.addressCode = cityData.placeId;
      data.location = cityData.location;
    }
    if (reqJobTitle) {
      data.jobTitle = reqJobTitle;
    }
    if (item.type === 'all') {
      this.setState({ reqJobTitle: '' });
      const { resumeStore } = this.props;
      if (resumeStore && resumeStore.resumeList) {
        this.props.resumeStore.resumeList = resumeStore.resumeList.map((x) => {
          x.isSelected = false;
          return x;
        });
      }
      data.jobTitle = '';
    }
    console.log('11111222', data);

    await this.getJobs(data);
  };

  setDefaultLanguage = (language) => {
    let lstr = '';
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        if (language === 'zh') {
          lstr = constant.languageMap.zh;
        } else if (language === 'en') {
          lstr = constant.languageMap.en;
        } else if (language === 'km') {
          lstr = constant.languageMap.km;
        } else if (language === 'vi') {
          lstr = constant.languageMap.vi;
        } else if (language === 'th') {
          lstr = constant.languageMap.th;
        } else if (language === 'ko') {
          lstr = constant.languageMap.ko;
        }
        this.props.userAction.setDefaultLanguage(lstr);
      }
    });
  };

  getJobs = async (data) => {
    try {
      const item = this.state.cityData;
      if (
        item &&
        item.locationId &&
        data.locationId !== item.locationId.value &&
        this.state.isSwitchCity
      ) {
        this.setState({ isSwitchCity: false });
        return;
      }
      const paramCityData = this.state.cityData;
      if (paramCityData.threeLevelCityData) {
        paramCityData.location = paramCityData.threeLevelCityData.location;
        paramCityData.addressCode = paramCityData.threeLevelCityData.placeId;
      } else {
        paramCityData.addressCode = paramCityData.placeId;
      }
      await this.props.jobAction.queryJobs({
        page: data.page,
        size: 10,
        ...data,
        ...paramCityData,
      });
      const { jobList, total } = this.props.jobStore;
      this.setState({
        hasMore: jobList.length > 0 && jobList[0].data.length < parseFloat(total),
        showLoading: false,
        refreshing: false,
      });
    } catch (e) {
      this.setState({ showLoading: false, refreshing: false });
    }
  };

  getData = async () => {
    try {
      const {
        isUrgent,
        recommendation,
        isNearby,
        isZh,
        isChina,
        educationIds,
        teamScaleIds,
        industyIds,
        workYearMax,
        workYearMin,
        reqJobTitle,
        cityData,
      } = this.state;
      const data = {
        page: this.page,
        employerScaleIds: teamScaleIds,
        industrialIds: industyIds,
        qualificationIds: educationIds,
      };
      if (isUrgent) {
        data.urgent = isUrgent;
      }
      if (recommendation) {
        data.recommendation = recommendation;
      }
      if (isNearby) {
        data.nearby = isNearby;
      }
      if (isZh) {
        data.language = 'zh';
      }
      if (isChina) {
        data.chinaCommerce = true;
      }
      if (workYearMax) {
        data.workYearMax = workYearMax;
      }
      if (workYearMin) {
        data.workYearMin = workYearMin;
      }
      if (reqJobTitle) {
        data.jobTitle = reqJobTitle;
      }
      if (cityData.locationId) {
        data.locationId = cityData.locationId;
      }
      if (cityData.placeId) {
        data.addressCode = cityData.placeId;
        data.location = cityData.location;
        data.locationId = cityData.locationId;
      }
      await this.getJobs(data);
    } catch (error) {
      this.setState({ showLoading: false, refreshing: false });
    }
  };

  getRequireFilterData = () => {
    const {
      workYearList,
      isUrgent,
      recommendation,
      isNearby,
      isZh,
      isChina,
      teamScaleIds,
      industyIds,
      reqJobTitle,
      cityData,
    } = this.state;
    let { qualificationList } = this.props.jobStore;
    qualificationList = qualificationList.map((item) => {
      item.isSelected = item.tempSelected;
      return item;
    });
    const temp = workYearList
      .map((item) => {
        item.isSelected = item.tempSelected;
        return item;
      })
      .filter((item) => item.isSelected === true);
    this.setState({
      educationIds: this.formarSubmitParam(qualificationList),
      reqFilterNumber: this.formarSubmitParam(qualificationList).length + temp.length,
    });
    const data = {
      page: 1,
      employerScaleIds: teamScaleIds,
      industrialIds: industyIds,
      qualificationIds: this.formarSubmitParam(qualificationList),
    };
    if (isUrgent) {
      data.urgent = isUrgent;
    }
    if (recommendation) {
      data.recommendation = recommendation;
    }
    if (isNearby) {
      data.nearby = isNearby;
    }
    if (isZh) {
      data.language = 'zh';
    }
    if (isChina) {
      data.chinaCommerce = true;
    }
    if (cityData.locationId) {
      data.locationId = cityData.locationId;
    }
    if (cityData.placeId) {
      data.addressCode = cityData.placeId;
      data.location = cityData.location;
    }
    if (reqJobTitle) {
      data.jobTitle = reqJobTitle;
    }
    if (temp.length > 0) {
      this.setState({
        workYearMin: temp[0].min,
        workYearMax: temp[0].max,
      });
      data.workYearMax = temp[0].max;
      data.workYearMin = temp[0].min;
    }
    this.getJobs(data);
  };

  getFilterCompanyData = () => {
    const {
      isUrgent,
      recommendation,
      isNearby,
      isZh,
      isChina,
      educationIds,
      workYearMax,
      workYearMin,
      reqJobTitle,
      cityData,
    } = this.state;
    let { teamScaleList, indutrialList } = this.props.jobStore;
    teamScaleList = teamScaleList.map((item) => {
      item.isSelected = item.tempSelected;
      return item;
    });
    indutrialList = indutrialList.map((item) => {
      item.isSelected = item.tempSelected;
      return item;
    });
    this.setState({
      teamScaleIds: this.formarSubmitParam(teamScaleList),
      industyIds: this.formarSubmitParam(indutrialList),
      comFilterNumber:
        this.formarSubmitParam(teamScaleList).length + this.formarSubmitParam(indutrialList).length,
    });
    const data = {
      page: 1,
      employerScaleIds: this.formarSubmitParam(teamScaleList),
      industrialIds: this.formarSubmitParam(indutrialList),
      qualificationIds: educationIds,
    };
    if (isUrgent) {
      data.urgent = isUrgent;
    }
    if (recommendation) {
      data.recommendation = recommendation;
    }
    if (isNearby) {
      data.nearby = isNearby;
    }
    if (isZh) {
      data.language = 'zh';
    }
    if (isChina) {
      data.chinaCommerce = true;
    }
    if (workYearMax) {
      data.workYearMax = workYearMax;
    }
    if (workYearMin) {
      data.workYearMin = workYearMin;
    }
    if (cityData.locationId) {
      data.locationId = cityData.locationId;
    }
    if (reqJobTitle) {
      data.jobTitle = reqJobTitle;
    }
    this.getJobs(data);
  };

  getPositionConfirm = () => {
    const { localCityData } = this.state;
    const data = {
      page: 1,
      locationId: localCityData.locationId,
    };
    localCityData.placeId && (data.addressCode = localCityData.placeId);
    const newCityData = localCityData;
    newCityData.cityName = this.getCityName(localCityData);
    this.setState(
      { showLocationModal: false, isSwitchCity: false, cityData: newCityData },
      async () => {
        this.props.jobAction.saveCurrentCity(localCityData);
        this.getJobs(data);
        const resl = await Session.isLogin();
        if (resl) {
          this.onCheckIntensions();
        }
      }
    );
  };

  getCurrentCity = async () => {
    let cityData = await this.props.jobAction.getCurrentCity();
    if (!cityData) {
      cityData = {
        placeId: '2200000000',
        nameEn: 'Phnom Penh',
        nameZh: '金边',
        nameKh: 'រាជធានីភ្នំពេញ',
        parentPlaceId: '0',
        placeType: 1,
        location: '11.5563738,104.9282099',
        locationId: 1,
        children: [],
        cityName: I18n.t('page_job_jin_bian_city'),
      };
      this.props.jobAction.saveCurrentCity(cityData);
    }
    return cityData;
  };

  // 定位失败，默认金边市
  getLocationFailure = async () => {
    const cityData = await this.getCurrentCity();
    this.setState({ cityData, isModalVisible: false });
    this.props.jobAction.saveCurrentCity(cityData);
    const data = { page: 1, locationId: 1 };

    this.getJobs(data);
    if (this.props.navigation.state.routeName === 'JobTab') {
      this.setState({
        isSwitchCity: false,
        showLocationModal: true,
        confirmTitle: I18n.t('page_get_location_to_select'),
      });
    }
  };

  initState = () => {
    this.setState({
      jobTypeLabel: I18n.t('page_job_text_all_job'),
      jobTypeList: [
        {
          label: I18n.t('page_job_text_all_job'),
          isSelected: true,
          type: 'all',
        },
        {
          label: I18n.t('page_job_text_immediate_work'),
          isSelected: false,
          type: 'urgent',
        },
        {
          label: I18n.t('page_job_text_recommend_work'),
          isSelected: false,
          type: 'recommend',
        },
        {
          label: I18n.t('page_job_text_nearby_work'),
          isSelected: false,
          type: 'nearby',
        },
        {
          label: I18n.t('page_job_text_zh_work'),
          isSelected: false,
          type: 'zh',
        },
        {
          label: I18n.t('page_job_text_china_work'),
          isSelected: false,
          type: 'china',
        },
      ],
    });
    this.getCurrentCity();
  };

  isLogin = async () => {
    this.props.cityAction.queryHotLocations();
    const resl = await Session.isLogin();
    if (resl) {
      await this.props.resumeAction.getResumes();
      this.props.jobAction.getJobStatistics();
      this.props.userAction.getCurrUserInfo();
    }
  };

  initJobList = async () => {
    this.setState({ refreshing: true });
    this.props.userAction.getBanners();
    const cityData = await this.getCurrentCity();
    this.setState({ cityData });
    const param = { page: 1 };
    if (cityData.locationId) {
      param.locationId = cityData.locationId;
    }
    this.getJobs(param);
  };

  formarSubmitParam = (array) => {
    const result = [];
    const tempArr = array.filter((item) => item.tempSelected === true);
    tempArr.forEach((item) => {
      result.push(item.value);
    });
    return result;
  };

  resetParam = (array) =>
    array.map((item) => {
      item.tempSelected = false;
      return item;
    });

  returnFormatParam = (array, index) => {
    array[index].isSelected = !array[index].isSelected;
    return array;
  };

  selectedCity = (item) => {
    if (item) {
      const {
        isUrgent,
        recommendation,
        isNearby,
        isZh,
        isChina,
        educationIds,
        teamScaleIds,
        industyIds,
        workYearMax,
        workYearMin,
        reqJobTitle,
      } = this.state;
      const data = {
        page: 1,
        employerScaleIds: teamScaleIds,
        industrialIds: industyIds,
        qualificationIds: educationIds,
      };
      if (isUrgent) {
        data.urgent = isUrgent;
      }
      if (recommendation) {
        data.recommendation = recommendation;
      }
      if (isNearby) {
        data.nearby = isNearby;
      }
      if (isZh) {
        data.language = 'zh';
      }
      if (isChina) {
        data.chinaCommerce = true;
      }
      if (workYearMax) {
        data.workYearMax = workYearMax;
      }
      if (workYearMin) {
        data.workYearMin = workYearMin;
      }
      if (item.value) {
        data.locationId = item.value;
      }
      if (reqJobTitle) {
        data.jobTitle = reqJobTitle;
      }
      if (item.placeId) {
        data.addressCode = item.placeId;
      }
      this.getJobs(data);
      const newCityData = item;
      newCityData.cityName = this.getCityName(item);
      this.setState({ cityData: newCityData });
      this.props.jobAction.saveCurrentCity(newCityData);
    }
  };

  localeFirstWidth = () => {
    if (I18n.locale === 'en') {
      return 26 * RVW;
    }
    if (I18n.locale === 'km') {
      return 40 * RVW;
    }
    return 35 * RVW;
  };

  localeMiddleWidth = () => {
    if (I18n.locale === 'en') {
      return 40 * RVW;
    }
    return 30 * RVW;
  };

  localeEndWidth = () => {
    if (I18n.locale === 'en') {
      return 34 * RVW;
    }
    if (I18n.locale === 'km') {
      return 30 * RVW;
    }
    return 35 * RVW;
  };

  onScan = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        navigation.navigate('cameraScan');
      } else {
        this.onShowAlert();
      }
    });
  };

  renderSwiper = () => {
    const {
      jobStore: { tempJobList },
      personStore: { bannerList },
    } = this.props;
    // return tempJobList && tempJobList.length === 0 && bannerList && bannerList.length === 0 ? (
    //   <View style={{ display: 'none' }} />
    // ) : (
    return (
      <MySwiper
        navigation={this.props.navigation}
        hasIntension={
          this.props.resumeStore.resumeList && this.props.resumeStore.resumeList.length > 0
        }
        intensionList={this.props.resumeStore.resumeList}
        onSelectIntension={this.onSelectIntension}
        onIntensionBadgeClick={this.onIntensionBadgeClick}
        hideSwiper={bannerList && bannerList.length === 0}
      />
    );
    // );
  };

  renderLeftIcon = () => {
    const { navigation } = this.props;
    const { cityData } = this.state;
    return (
      <TouchableOpacity
        onPress={() => {
          this.onCancelTap();
          navigation.navigate('cityList', {
            needSave: true,
            onSelect: (item) => {
              this.selectedCity(item);
            },
          });
        }}
      >
        <View style={jobStyle.searchBar}>
          <Icon name="place" iconStyle={{ marginLeft: -2 }} size={16} color={baseBlueColor} />
          <Text
            numberOfLines={1}
            style={[
              jobStyle.cityName,
              {
                marginRight: (cityData.cityName && cityData.cityName.length) > 3 ? 4 : 0,
                width: util.calLength(cityData.cityName),
              },
            ]}
          >
            {cityData.cityName}
          </Text>
          <Divider style={jobStyle.divider} />
        </View>
      </TouchableOpacity>
    );
  };

  renderSearch = () => {
    const { navigation } = this.props;
    return (
      <View style={{ flexDirection: 'row' }}>
        <Input
          inputContainerStyle={jobStyle.renderSearchInput}
          inputStyle={{
            color: titleColor,
            fontSize: 12,
          }}
          editable={false}
          returnKeyType="search"
          placeholder={I18n.t('page_job_ph_search')}
          placeholderTextColor={desColor}
          leftIcon={this.renderLeftIcon()}
        />
        <TouchableOpacity
          style={jobStyle.renderSearchView}
          onPress={() => {
            this.onCancelTap();
            navigation.navigate('jobSearch', {
              onSelect: (item) => {
                this.selectedCity(item);
              },
            });
          }}
        />
      </View>
    );
  };

  renderFilterView = () => {
    const { urgentShow, companyShow, requireShow, comFilterNumber, reqFilterNumber } = this.state;
    const downIcon = <Icon name="chevron-small-down" type="entypo" size={20} color={desColor} />;
    const upIcon = <Icon name="chevron-small-up" type="entypo" size={20} color={baseBlueColor} />;
    return (
      <TouchableOpacity
        onPress={() => {
          this.onFilterViewTap();
        }}
      >
        <View style={jobStyle.jobFilter}>
          <TouchableOpacity
            onPress={() => {
              this.onJobFilter();
            }}
          >
            <View
              style={[
                jobStyle.jobFilterFirst,
                {
                  width: this.localeFirstWidth(),
                  paddingHorizontal: I18n.locale === 'zh' ? 8 : 16,
                  paddingLeft: 20,
                },
              ]}
            >
              <Text
                numberOfLines={1}
                style={[
                  jobStyle.jobFilterText,
                  { color: urgentShow ? baseBlueColor : titleColor, textAlign: 'center' },
                ]}
              >
                {this.state.jobTypeLabel}
              </Text>
              {urgentShow ? upIcon : downIcon}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.onCompanyFilter();
            }}
          >
            <View
              style={[
                jobStyle.jobFilterMiddle,
                {
                  width: this.localeMiddleWidth(),
                  paddingHorizontal: I18n.locale === 'en' ? 10 : 6,
                },
              ]}
            >
              <Text
                numberOfLines={1}
                style={[
                  jobStyle.jobFilterText,
                  { color: companyShow ? baseBlueColor : titleColor },
                ]}
              >
                {comFilterNumber > 0
                  ? `${I18n.t('page_job_text_company')}(${comFilterNumber})`
                  : I18n.t('page_job_text_company')}
              </Text>
              {companyShow ? upIcon : downIcon}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.onRequireFilter();
            }}
          >
            <View
              style={[
                jobStyle.jobFilterEnd,
                { width: this.localeEndWidth(), paddingHorizontal: I18n.locale === 'zh' ? 16 : 8 },
              ]}
            >
              <Text
                numberOfLines={1}
                style={[
                  jobStyle.jobFilterText,
                  { color: requireShow ? baseBlueColor : titleColor },
                ]}
              >
                {' '}
                {reqFilterNumber > 0
                  ? `${I18n.t('page_job_text_require')}(${reqFilterNumber})`
                  : I18n.t('page_job_text_require')}
              </Text>
              {requireShow ? upIcon : downIcon}
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  renderFooter = () =>
    this.state.hasMore ? (
      <Text style={{ textAlign: 'center', paddingVertical: 12, color: subTitleColor }}>
        {I18n.t('page_job_text_loading')}
      </Text>
    ) : (
      <Text style={{ display: 'none' }} />
    );

  renderCompanyList = ({ item }) => {
    return <JobItem item={item} onPress={this.onToJobDetail} />;
  };

  renderUrgentModal = () => (
    <Modal
      animationType="none"
      transparent
      visible={this.state.urgentShow}
      onRequestClose={() => {}}
      statusBarTranslucent
    >
      <View style={jobStyle.modalStyle}>
        {this.renderHeader()}
        {this.renderFilterView()}
        {this.renderIsUrgentList()}
      </View>
    </Modal>
  );

  renderIsUrgentList = () => {
    const { urgentShow, jobTypeList } = this.state;
    return (
      <View style={{ width: '100%', height: '100%' }}>
        <View
          style={[
            jobStyle.subView,
            { height: 48 * jobTypeList.length, display: urgentShow ? 'flex' : 'none' },
          ]}
        >
          <View style={{ backgroundColor: '#fff' }}>
            {jobTypeList.map((item) => (
              <ListItem
                key={item.label}
                containerStyle={{
                  borderBottomColor: '#eee',
                  borderBottomWidth: 0.5,
                  paddingHorizontal: 12,
                  minHeight: 48,
                  backgroundColor: item.isSelected ? '#E1F2FC' : '#fff',
                }}
                onPress={() => {
                  this.onSelectUrgent(item);
                }}
                rightIcon={
                  item.isSelected ? (
                    <Icon type="feather" name="check" size={18} color={baseBlueColor} />
                  ) : (
                    <Icon type="feather" name="check" size={18} color="#fff" />
                  )
                }
                title={item.label}
                titleStyle={{
                  color: titleColor,
                  fontSize: 14,
                }}
              />
            ))}
          </View>
        </View>
        <View style={{ width: '100%', height: '80%' }}>
          <TouchableOpacity onPress={this.onCancelTap}>
            <Text style={{ width: '100%', height: '100%' }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderCompanyModal = () => (
    <Modal
      animationType="none"
      transparent
      visible={this.state.companyShow}
      onRequestClose={() => {}}
      statusBarTranslucent
    >
      <View style={jobStyle.modalStyle}>
        {this.renderHeader()}
        {this.renderFilterView()}
        {this.renderCompanyFilterData()}
      </View>
    </Modal>
  );

  renderRequireModal = () => (
    <Modal
      animationType="none"
      transparent
      visible={this.state.requireShow}
      onRequestClose={() => {}}
      statusBarTranslucent
    >
      <View style={jobStyle.modalStyle}>
        {this.renderHeader()}
        {this.renderFilterView()}
        {this.renderRequireFilterData()}
      </View>
    </Modal>
  );

  renderRightHeader = () => {
    const { navigation } = this.props;
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity onPress={() => this.onSearch()}>
          <Image style={{ width: 20, height: 20 }} source={res.search} />
        </TouchableOpacity>
        <View
          style={{
            width: 0.5,
            height: 20,
            backgroundColor: '#FFF',
            marginHorizontal: 17,
            opacity: 0.25,
          }}
        />
        <TouchableOpacity onPress={this.onScan}>
          <Image style={{ width: 18, height: 18 }} source={res.scan} />
        </TouchableOpacity>
      </View>
    );
  };

  renderHeader = () => (
    <Header
      statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
      containerStyle={[headerStyle.wrapper]}
      // centerComponent={this.renderSearch()}
      centerComponent={this.renderHeaderLeft()}
      centerContainerStyle={{ paddingHorizontal: 0 }}
      rightComponent={this.renderRightHeader()}
      placement="left"
    />
  );

  getCityName = (item) => {
    let areaNameData = util.getCityNameWithCityData(JSON.parse(JSON.stringify(item)));
    if (item.twoLevelCityData && item.twoLevelCityData.placeId) {
      areaNameData = `${areaNameData} ${util.getCityNameWithCityData(
        JSON.parse(JSON.stringify(item)).twoLevelCityData
      )}`;
    }

    if (item.threeLevelCityData && item.threeLevelCityData.placeId) {
      areaNameData = `${areaNameData} ${util.getCityNameWithCityData(
        JSON.parse(JSON.stringify(item)).threeLevelCityData
      )}`;
    }
    return areaNameData;
  };

  saveArea = (item) => {
    const areaNameData = this.getCityName(item);
    const newCityData = JSON.parse(JSON.stringify(item));
    newCityData.cityName = areaNameData;
    this.setState(
      {
        isSwitchCity: false,
        cityData: newCityData,
      },
      () => {
        const data = JSON.parse(JSON.stringify(item));
        data.value = data.locationId;
        data.page = 1;
        data.addressCode = data.placeId;
        data.code = `LOCATION_${data.locationId}`;
        this.getJobs(data);
      }
    );
    this.props.jobAction.saveCurrentCity(newCityData);
  };

  pushAction = () => {
    const { navigation } = this.props;
    const { cityData } = this.state;
    if (
      cityData.locationId &&
      cityData.locationId != 32 &&
      cityData.locationId != 30 &&
      cityData.locationId.locationId != 32
    ) {
      this.onCancelTap();
      navigation.navigate('selectAreaPage', {
        needSave: true,
        cityData,
        onSelect: (item) => {
          this.saveArea(item);
        },
      });
    } else {
      navigation.navigate('cityList', {
        needSave: true,
        onSelect: (item) => {
          this.saveArea(item);
        },
      });
    }
  };

  renderHeaderLeft = () => {
    const { cityData } = this.state;
    return (
      <TouchableOpacity onPress={() => this.pushAction()}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Icon name="place" iconStyle={{ marginRight: 2 }} size={16} color="#FFF" />
          <Text numberOfLines={1} style={{ fontSize: 12, color: '#FFF', maxWidth: '80%' }}>
            {' '}
            {cityData.cityName}
          </Text>
          <Image style={{ width: 5, height: 5, marginLeft: 6, marginTop: 5 }} source={res.down} />
        </View>
      </TouchableOpacity>
    );
  };

  renderCompanyFilterData = () => {
    const { teamScaleList, indutrialList } = this.props.jobStore;
    const { companyShow } = this.state;
    return (
      <View style={{ width: '100%', height: '100%' }}>
        <View style={[jobStyle.subView, { display: companyShow ? 'flex' : 'none' }]}>
          <ScrollView>
            <View>
              <Text style={jobStyle.contentText}>{I18n.t('page_job_text_employer_scale')}</Text>
              <View style={jobStyle.requireFilter}>
                {teamScaleList.map((item, i) => (
                  <Badge
                    key={`${i + 1}`}
                    containerStyle={jobStyle.companyFilter}
                    onPress={() => this.onBadgeClick(item)}
                    value={`${item.label}${I18n.t('page_job_text_person')}`}
                    badgeStyle={[
                      jobStyle.badgeStyle,
                      item.tempSelected && { backgroundColor: baseBlueColor },
                    ]}
                    textStyle={[jobStyle.badgeTextStyle, item.tempSelected && { color: '#fff' }]}
                  />
                ))}
              </View>
              <Text style={jobStyle.contentText}>{I18n.t('page_job_text_industry')}</Text>
              <View style={jobStyle.requireFilter}>
                {indutrialList.map((item, i) => (
                  <Badge
                    key={`${i + 1}`}
                    containerStyle={jobStyle.companyFilter}
                    onPress={() => this.onBadgeClick(item)}
                    value={` ${item.label} `}
                    badgeStyle={[
                      jobStyle.badgeStyle,
                      item.tempSelected && { backgroundColor: baseBlueColor },
                    ]}
                    textStyle={[jobStyle.badgeTextStyle, item.tempSelected && { color: '#fff' }]}
                  />
                ))}
              </View>
            </View>
          </ScrollView>
          <View style={jobStyle.buttonView}>
            <TouchableHighlight
              underlayColor="transparent"
              style={[jobStyle.buttonStyle, { backgroundColor: '#fff' }]}
              onPress={() => this.onResetTap('company')}
            >
              <Text style={jobStyle.buttonText}>{I18n.t('page_job_btn_reset')}</Text>
            </TouchableHighlight>
            <TouchableHighlight
              underlayColor="transparent"
              style={jobStyle.buttonStyle}
              onPress={() => this.onConfirmTap('company')}
            >
              <Text style={[jobStyle.buttonText, { color: '#fff' }]}>
                {I18n.t('page_job_btn_coonfirm')}
              </Text>
            </TouchableHighlight>
          </View>
        </View>
        <View style={{ width: '100%', height: '28%' }}>
          <TouchableOpacity onPress={this.onCancelTap}>
            <Text style={{ width: '100%', height: '100%' }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderRequireFilterData = () => {
    const { qualificationList } = this.props.jobStore;
    const { workYearList, requireShow } = this.state;
    return (
      <View style={{ width: '100%', height: '100%' }}>
        <View style={[jobStyle.subView, { display: requireShow ? 'flex' : 'none' }]}>
          <ScrollView>
            <View>
              <Text style={jobStyle.contentText}>{I18n.t('page_job_text_education')}</Text>
              <View style={jobStyle.requireFilter}>
                {qualificationList.map((item, i) => (
                  <Badge
                    key={`${i + 1}`}
                    containerStyle={jobStyle.companyFilter}
                    onPress={() => this.onBadgeClick(item)}
                    value={` ${item.label} `}
                    badgeStyle={[
                      jobStyle.badgeStyle,
                      item.tempSelected && { backgroundColor: baseBlueColor },
                    ]}
                    textStyle={[jobStyle.badgeTextStyle, item.tempSelected && { color: '#fff' }]}
                  />
                ))}
              </View>
              <Text style={jobStyle.contentText}>{I18n.t('page_job_text_work_years')}</Text>
              <View style={jobStyle.requireFilter}>
                {workYearList.map((item, i) => (
                  <Badge
                    key={`${i + 1}`}
                    containerStyle={jobStyle.companyFilter}
                    onPress={() => this.onExperienceBadgeClick(item)}
                    value={` ${item.name}${I18n.t(
                      item.name === workYearList[workYearList.length - 1].name
                        ? 'page_job_text_year_more'
                        : 'page_job_text_year'
                    )} `}
                    badgeStyle={[
                      jobStyle.badgeStyle,
                      item.tempSelected && { backgroundColor: baseBlueColor },
                    ]}
                    textStyle={[jobStyle.badgeTextStyle, item.tempSelected && { color: '#fff' }]}
                  />
                ))}
              </View>
            </View>
          </ScrollView>
          <View style={jobStyle.buttonView}>
            <TouchableHighlight
              underlayColor="transparent"
              style={[jobStyle.buttonStyle, { backgroundColor: '#fff' }]}
              onPress={() => this.onResetTap('require')}
            >
              <Text style={jobStyle.buttonText}>{I18n.t('page_job_btn_reset')}</Text>
            </TouchableHighlight>
            <TouchableHighlight
              underlayColor="transparent"
              style={jobStyle.buttonStyle}
              onPress={() => this.onConfirmTap('require')}
            >
              <Text style={[jobStyle.buttonText, { color: '#fff' }]}>
                {I18n.t('page_job_btn_coonfirm')}
              </Text>
            </TouchableHighlight>
          </View>
        </View>
        <View style={{ width: '100%', height: '28%' }}>
          <TouchableOpacity onPress={this.onCancelTap}>
            <Text style={{ width: '100%', height: '100%' }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderEmptyComponent = () =>
    this.state.refreshing ? (
      <View />
    ) : (
      <View style={{ marginTop: '40%', justifyContent: 'center', alignItems: 'center' }}>
        <Image style={{ width: '24%', height: 120 }} source={res.noData} />
      </View>
    );

  renderEmpty = () => {
    const { showLoading, urgentShow, refreshing } = this.state;
    const {
      jobStore: { jobList },
    } = this.props;
    return !showLoading &&
      jobList.length &&
      jobList[0] &&
      jobList[0].data &&
      jobList[0].data.length === 0 ? (
      <View>
        {urgentShow ? <Text style={{ display: 'none' }} /> : this.renderSwiper()}
        {urgentShow ? <Text style={{ display: 'none' }} /> : this.renderFilterView()}
        <View style={{ marginTop: '36%', justifyContent: 'center', alignItems: 'center' }}>
          <Image style={{ width: '24%', height: 120 }} source={res.noData} />
        </View>
      </View>
    ) : (
      <SectionList
        ref={(ref) => {
          this.onToTargetIndex = ref;
        }}
        renderSectionHeader={this.renderFilterView}
        ListEmptyComponent={this.renderEmptyComponent}
        initialNumToRender={5}
        renderSectionFooter={this.renderFooter}
        renderItem={this.renderCompanyList}
        sections={jobList.slice()}
        ListHeaderComponent={this.renderSwiper}
        keyExtractor={(item, index) => index + item}
        refreshing={refreshing}
        onRefresh={this.onRefresh}
        onEndReachedThreshold={0.1}
        enableEmptySections
        stickySectionHeadersEnabled
        onEndReached={() => this.onLoadMore()}
      />
    );
  };

  render() {
    const { navigation } = this.props;
    const { cityData, localCityData } = this.state;
    return (
      <View style={jobStyle.jobContainer}>
        {this.renderHeader()}
        {this.state.showLoading ? (
          <View style={jobStyle.loadingStyle}>
            <Image style={{ width: 100, height: 100 }} source={res.loadingImg} />
          </View>
        ) : (
          this.renderEmpty()
        )}
        {this.renderUrgentModal()}
        {this.renderCompanyModal()}
        {this.renderRequireModal()}
        <GuideModal
          isOpen={this.state.isModalVisible}
          nav={navigation}
          showType={this.state.showType}
          hideLocationModal={() => {
            this.setState({ showLocationModal: false });
          }}
          closeModal={() => {
            this.setState({ isModalVisible: false });
          }}
          hideModal={() => {
            this.setState({ isModalVisible: false });
          }}
          showModal={() => {
            this.setState({ isModalVisible: true });
          }}
          onCheckIntensions={() => {
            CheckFlow.continue(1);
          }}
        />
        <LocationModal
          dismiss={() => {
            this.setState({ showLocationModal: false, isSwitchCity: false });
            CheckFlow.continue();
          }}
          isOpen={this.state.showLocationModal}
          memo={`${I18n.t('page_get_location_to_view_at')}${localCityData.cityName || ''}，${I18n.t(
            'page_get_location_switch_or_not'
          )}`}
          confirmTitle={this.state.confirmTitle}
          isSwitchCity={this.state.isSwitchCity}
          getPositionConfirm={this.getPositionConfirm}
          selectedCity={() => {
            this.setState({ showLocationModal: false });
            this.props.navigation.navigate('cityList', {
              needSave: true,
              onSelect: (item) => {
                CheckFlow.continue();
                this.selectedCity(item);
              },
              onContinueCheck: () => {
                CheckFlow.continue();
              },
            });
          }}
        />
        <CheckVersionModal />
        <BindPhoneModal
          ref={(ref) => (this.bindPhoneModal = ref)}
          nav={navigation}
          onCheckIntensions={() => {
            CheckFlow.continue(1);
          }}
        />
      </View>
    );
  }
}
