import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { Text, View, TextInput, TouchableOpacity, ScrollView, Keyboard } from 'react-native';
import { Header, Icon, CheckBox } from 'react-native-elements';
import { dynamicStyle, globalStyle, headerStyle, bgColor } from '../../themes';
import GoBack from '../../components/goback';
import I18n from '../../i18n';
import LoadingModal from '../../components/loadingModal';
import util from '../../util';
import CompanyService from '../../api/companyService';
import uiUtil from '../../util/uiUtil';
import NavigationService from '../../navigationService';
import constant from '../../store/constant';

function getStyle() {
  return {
    container: {
      flex: 1,
      backgroundColor: '#fff',
    },
    editContent: {
      padding: 15,
    },
    inputText: {
      height: 300,
      fontSize: 16,
      color: '#333',
      borderWidth: 1,
      borderColor: bgColor,
      paddingLeft: 8,
    },
    editInputText: {
      height: 300,
    },
    editTextCount: {
      color: '#999',
      textAlign: 'right',
      marginTop: 10,
    },
    numberCount: {
      color: 'orange',
    },
    line: {
      height: 1,
      backgroundColor: '#ccc',
      marginTop: 15,
      marginBottom: 15,
    },
  };
}

@inject('dynamicStore', 'dynamicAction')
@observer
export default class AddQuickText extends Component {
  style = getStyle();
  constructor(props) {
    super(props);
    this.showLoading = false;
    this.item = props.navigation.state.params?.item;
    this.list = props.navigation.state.params?.list || [];
    this.state = {
      msgText: this.item?.text || '',
      count: this.item?.text?.length || 0,
    };
  }

  publish = async () => {
    Keyboard.dismiss();
    if (!this.state.msgText.trim()) {
      toast.show(I18n.t('page_chat_tips_quick_text'));
      return;
    }

    try {
      uiUtil.showGlobalLoading();
      const { msgText } = this.state;
      if (this.item) {
        this.item.text = msgText;
      }
      if (this.list.length > 0 && this.item) {
        this.list = this.list.map((item) => {
          if (item.id === this.item.id) {
            return { ...item, text: msgText };
          }
          return item;
        });
      } else {
        this.list.push({ ...this.item, text: msgText });
      }
      const res = await CompanyService.saveQuickText(this.list);
      if (res.successful) {
        this.props.navigation.state.params?.onRefresh?.();
        NavigationService.goBack();
        global.emitter.emit(constant.event.updateQuickText);
      }
      uiUtil.showRequestResult(res);
    } catch (error) {
      uiUtil.showRequestResult(error);
    }
  };

  render() {
    const { style } = this;
    const { navigation } = this.props;
    const { msgText } = this.state;
    return (
      <View style={style.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={[headerStyle.wrapper]}
          centerComponent={{
            text: I18n.t('page_chat_add_quick_text'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          rightComponent={
            <TouchableOpacity
              onPress={() =>
                util.HandlerOnceTap(() => {
                  this.publish();
                })
              }
            >
              <Text style={[headerStyle.rightBtn, { color: '#fff' }]}>
                {I18n.t('page_resume_btn_save')}
              </Text>
            </TouchableOpacity>
          }
        />
        <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
          <View style={style.editContent}>
            <TextInput
              style={[style.inputText, style.editInputText]}
              blurOnSubmit={false}
              placeholder={I18n.t('page_chat_ph_quick_text')}
              underlineColorAndroid="transparent"
              multiline
              maxLength={400}
              ref={(ref) => {
                this.inputText = ref;
              }}
              onChangeText={(text) => {
                this.setState({
                  count: text.length,
                  msgText: text,
                });
              }}
              defaultValue={msgText}
              textAlignVertical="top"
            />
            <Text style={style.editTextCount}>
              <Text style={style.numberCount}>{this.state.count}</Text>
              /400
            </Text>
          </View>
        </ScrollView>

        <LoadingModal isOpen={this.showLoading} loadingTips={false} />
      </View>
    );
  }
}
