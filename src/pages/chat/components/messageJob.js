import React, { Component } from 'react';
import { StyleSheet } from '../../../components';
import messageDao from '../../../database/dao/messageDao';
import SentryUtil from '../../../util/sentryUtil';
import JobItem from '../../../components/listItem/jobItem';
import jobService from '../../../api/jobService';
import { inject, observer } from 'mobx-react';
import NavigationService from '../../../navigationService';
import jobAction from '../../../store/actions/job';
import companyAction from '../../../store/actions/company';

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 0,
    marginHorizontal: 12,
    marginBottom: 20,
    borderRadius: 10,
    shadowColor: 'rgba(0, 0, 0, 0.05)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 3,
  },
});

/**
 * 职位/岗位消息，求职者查看职位时，点按聊天发送的消息类型
 * <AUTHOR>
 */
@inject('userStore')
@observer
export default class MessageJob extends Component {
  constructor(props) {
    super(props);
    this.localExtra =
      (props.currentMessage.localExtra && SentryUtil.parse(props.currentMessage.localExtra)) || {};
    this.state = {
      job: this.localExtra.job,
    };
  }

  componentDidMount() {
    this.getJobDetail();
  }

  componentWillUnmount() {
    this.isUnmount = true;
  }

  getJobDetail = async () => {
    try {
      // if (this.props.userStore.isCompany) return;
      let { job } = this.state;
      // 上次获取时间小于一小时，暂不刷新
      if (job?.getTime && Date.now() - job.getTime < 36000000) return;
      const { jobId } = this.props.currentMessage;
      if (!jobId) return;
      if (this.props.userStore.isCompany) {
        job = await companyAction.queryJobsByJobId(jobId);
      } else {
        job = await jobService.querySingleJobs(jobId);
        await jobAction.checkAndFlagCommunicated(jobId, this.localExtra);
      }
      job.getTime = Date.now();
      this.localExtra = { job };
      this.props.currentMessage.localExtra = JSON.stringify(this.localExtra);
      messageDao.updateLocalExtra(this.props.currentMessage);
      if (this.isUnmount) return;
      this.setState({ job });
    } catch (e) {
      console.warn('messageJob getJobDetail', e);
    }
  };

  onPress = (job) => {
    if (this.props.userStore.isCompany) {
      // NavigationService.push('epjobDetail', { item: job, isChat: true });
      NavigationService.push('jobDetail', { detail: job, isChat: true, isPreview: true });
    } else {
      NavigationService.push('jobDetail', { detail: job, isChat: true });
    }
  };

  render() {
    const { job } = this.state;
    if (!job) return null;
    return (
      <JobItem
        onPress={this.onPress}
        item={job}
        container={styles.container}
        withoutFeedback
        isChat
      />
    );
  }
}
