import React from 'react';
import { inject, observer } from 'mobx-react';
import { FlatList, Icon, Text, Touchable, View } from '../../../components';
import { WIDTH } from '../../../common';
import NavigationService from '../../../navigationService';
import CompanyService from '../../../api/companyService';
import constant from '../../../store/constant';
import I18n from '../../../i18n';

function getStyle() {
  return {
    container: {
      height: WIDTH(230),
    },
    scrollView: {
      flex: 1,
      paddingTop: 10,
      paddingBottom: 100,
    },
    itemWrapper: {
      marginHorizontal: 16,
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e5e5',
    },
    titleText: {
      fontSize: 14,
      color: '#333',
    },
    bottomBtns: {
      backgroundColor: '#fff',
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      height: 40,
      // shadowColor: 'black',
      // shadowOffset: { h: 2 },
      // shadowOpacity: 0.2,
      // elevation: 3,
    },
    btnItemLine: {
      width: 1,
      height: 15,
      backgroundColor: '#e5e5e5',
    },
    btnItem: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      height: 40,
    },
    btnItemText: {
      fontSize: 12,
      color: '#333',
      marginLeft: 3,
    },
  };
}

/**
 * 聊天页面，底部快捷文本
 * <AUTHOR>
 */
@inject('userStore')
@observer
export default class MessageQuickText extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.style = getStyle();
    this.state = {
      quickTextList: [],
      refreshing: true,
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.updateQuickText, this.loadData);
    this.loadData();
  }
  componentWillUnmount() {
    global.emitter.off(constant.event.updateQuickText, this.loadData);
  }

  onRefresh = () => {
    this.setState({ refreshing: true }, () => {
      this.loadData();
    });
  };

  loadData = async () => {
    try {
      const { isCompany } = this.props.userStore;
      const res = await CompanyService.queryQuickText();
      this.setState({ quickTextList: res, refreshing: false });
    } catch (error) {
      this.setState({ refreshing: false });
    }
  };

  onAdd = () => {
    if (this.state.quickTextList.length >= 10) {
      toast.show(I18n.t('page_chat_tips_add_quick_text'));
      return;
    }
    NavigationService.navigate('addQuickText', {
      onRefresh: this.loadData,
      list: this.state.quickTextList,
    });
  };
  onManage = () => {
    NavigationService.navigate('chatQuickTextList');
  };

  renderItem = ({ item }) => {
    const { style } = this;
    return (
      <Touchable
        onPress={() => this.props.onPressQuickText(item.text)}
        style={style.itemWrapper}
        disableDebounce
      >
        <Text style={style.titleText} numberOfLines={3}>
          {item.text}
        </Text>
      </Touchable>
    );
  };

  keyExtractor = (item) => item.id;

  render() {
    const { hide } = this.props;
    if (hide) {
      return null;
    }
    const { style } = this;
    const { quickTextList, refreshing } = this.state;
    return (
      <View style={style.container}>
        <FlatList
          style={style.scrollView}
          data={quickTextList}
          renderItem={this.renderItem}
          keyExtractor={this.keyExtractor}
          ListFooterComponent={() => <View style={{ height: 10 }} />}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={this.onRefresh}
        />

        <View style={style.bottomBtns}>
          <Touchable style={style.btnItem} onPress={this.onAdd}>
            <Icon type="material" name="playlist-add" size={20} color="#2089DC" />
            <Text style={style.btnItemText}>{I18n.t('page_chat_text_add')}</Text>
          </Touchable>
          <View style={style.btnItemLine} />
          <Touchable style={style.btnItem} onPress={this.onManage}>
            <Icon type="foundation" name="folder-add" size={20} color="#2089DC" />
            <Text style={style.btnItemText}>{I18n.t('op_manage_title')}</Text>
          </Touchable>
        </View>
      </View>
    );
  }
}
