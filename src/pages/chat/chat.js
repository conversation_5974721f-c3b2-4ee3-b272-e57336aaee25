import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { AlertPro, Text, Touchable, View } from '../../components';
import I18n from '../../i18n';
import styles from '../../themes';
import { footerHeight, statusBarHeight } from '../../common';
import NavigationService from '../../navigationService';
import chatSessionDao from '../../database/dao/chatSessionDao';
import constant from '../../store/constant';
import ChatItem from './components/chatItem';
import Debounce from 'debounce-decorator';
import { SwipeListView } from 'react-native-swipe-list-view';
import NoData from '../../components/empty/noData';
import chatMessageUtil from '../../database/chatMessageUtil';
import ActionSheet from '../../components/modal/actionSheet';

function getComponentStyle(theme) {
  return {
    chatContainer: {
      flex: 1,
      backgroundColor: theme.listBgColor,
    },
    listSeparator: { height: 1, backgroundColor: '#F5F8FB', marginHorizontal: 15 },
    topContainer: {
      // paddingHorizontal: 15,
      // paddingBottom: 30,
      backgroundColor: theme.primaryColor,
    },
    searchBox: {
      marginTop: 10,
      backgroundColor: theme.primaryBgColor,
      paddingTop: 15,
      paddingBottom: 15,
      paddingHorizontal: 15,
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: statusBarHeight,
      height: statusBarHeight + 56,
      paddingVertical: 6,
      paddingHorizontal: 18,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: theme.fontWeightMedium,
      color: theme.titleFontColor,
      lineHeight: 33,
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    userAvatarBox: {
      borderRadius: 14,
      width: 36,
      height: 36,
      borderWidth: 4,
      borderColor: 'rgba(255,255,255,0.2)',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      shadowOffset: {
        width: -5,
        height: 0,
      },
      shadowRadius: 20,
      shadowColor: 'rgba(0,0,0,0.15)',
      shadowOpacity: 1,
      marginLeft: -5,
    },
    userAvatar: {
      width: 32,
      height: 32,
      borderRadius: 12,
    },
    chatAdd: {
      width: 20,
      height: 20,
    },
    addmodal: {
      flex: 1,
      // backgroundColor:'red'
    },
    chatInformation: {
      position: 'absolute',
      right: 0,
      bottom: 0,
    },
    addBox: {
      position: 'absolute',
      top: footerHeight + 54,
      right: 14,
      backgroundColor: '#F1FEFF',
      borderRadius: 10,
      paddingLeft: 19,
      paddingRight: 11,
      paddingVertical: 5,
      width: 180,
      shadowOffset: {
        width: -5,
        height: 0,
      },
      shadowRadius: 20,
      shadowColor: 'rgba(0,0,0,0.15)',
      shadowOpacity: 1,
    },
    addItem: {
      borderBottomWidth: 1,
      borderColor: '#EEEEEE',
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 10,
    },
    addItemText: {
      fontSize: 16,
      color: '#333333',
      lineHeight: 22,
      marginLeft: 16,
    },
    searchTextBox: {
      paddingRight: 15,
    },
    searchBtn: {
      fontSize: 14,
      color: '#5D6CC1',
      fontWeight: '500',
      lineHeight: 20,
      paddingLeft: 3,
    },
    subContainer: {
      flex: 1,
    },
    backTextWhite: {
      color: '#ffffff',
      fontSize: theme.fontSizeM,
      fontWeight: theme.fontWeightBold,
    },
    rowBack: {
      alignItems: 'center',
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingLeft: 15,
    },
    backRightBtn: {
      alignItems: 'center',
      bottom: 0,
      justifyContent: 'center',
      position: 'absolute',
      top: 0,
      width: 75,
      backgroundColor: '#FF4B5A',
    },
    backRightBtnRight: {
      right: 0,
    },
    segmentBox: {
      flexDirection: 'row',
      alignItems: 'center',
      width: 200,
      height: 32,
      borderRadius: 10,
      backgroundColor: 'rgba(255,255,255,0.2)',
      overflow: 'hidden',
    },
    segmentBoxCell: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '50%',
      height: '100%',
      borderRadius: 9,
    },
    segmentIcon: {
      marginRight: 7,
    },
    segmentText: {
      fontSize: 13,
      color: '#fff',
    },
    segmentBoxCellActive: {
      backgroundColor: '#E5F1F3',
    },
    segmentTextActive: {
      color: theme.primaryColor,
    },
    filterBox: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      paddingBottom: 10,
      paddingHorizontal: 23,
    },
    filterSpace: {
      width: 1,
      height: 10,
      backgroundColor: '#E5EBEB',
      marginHorizontal: 23,
    },
    filterFav: {
      marginRight: 23,
    },
    filterBoxContainer: {
      backgroundColor: '#fff',
    },
    selcetChatBook: {
      marginHorizontal: 15,
      paddingHorizontal: 0,
      borderBottomWidth: 1,
      borderColor: '#ECF5F6',
    },
    chatbookText: {
      fontSize: 15,
      color: '#333333',
      marginLeft: 14,
    },
  };
}
/**
 * 聊天页
 */
@inject('userStore', 'pageStore', 'settingsStore', 'imAction', 'chatStore', 'meetingAction')
@observer
export default class Chat extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      searchValue: '',
      sessionList: [],
      currentSessionList: [],
      // newReqCount: 0,
      showAlert: false,
      deleteItem: '',
    };
  }

  componentDidMount() {
    console.log('chat componentDidMount');
    this.willFocusSubscription = this.props.navigation.addListener('willFocus', this.onWillFocus);
    global.emitter.on(constant.event.chatSessionChange, this.onChatSessionChange);
    global.emitter.on(constant.event.tabbarChanged, this.onTabBarChanged);
    this.onRefresh(false);
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
    global.emitter.off(constant.event.chatSessionChange, this.onChatSessionChange);
    global.emitter.off(constant.event.tabbarChanged, this.onTabBarChanged);
  }

  onChatSessionChange = () => {
    this.onWillFocus(false);
  };

  onWillFocus = (refreshUnread = true) => {
    const currentScreen = NavigationService.getCurrentScreen();
    if (currentScreen === 'main') {
      const selectedTab = this.props.userStore.isCompany
        ? this.props.pageStore.selectedTab
        : this.props.settingsStore.selectedTab;
      if (selectedTab === constant.tabs.chat) {
        this.onRefresh(refreshUnread);
      }
    }
  };

  onTabBarChanged = ({ name }) => {
    if (name === constant.tabs.chat) {
      this.onRefresh(true);
    }
  };

  // 刷新
  onRefresh = (refreshUnread) => {
    this.loadData();
    if (refreshUnread) {
      this.props.imAction.getAllOfflineMessageList();
    }
  };

  loadData = async () => {
    const { imId, isManage } = this.props.userStore;
    let result = await chatSessionDao.querySessionList({ ownerId: imId });
    const res = await this.props.imAction.queryConversationItems();
    let remoteSessionList = res.map((item) => JSON.parse(item.extra));
    remoteSessionList.forEach((remoteItem) => {
      const localIndex = result.findIndex((item) => item.sessionId === remoteItem.sessionId);
      if (localIndex > -1) {
        result[localIndex] = { ...remoteItem, ...result[localIndex] };
      } else {
        result.push(remoteItem);
      }
    });
    result.sort((a, b) => {
      return b.lastMsgTime - a.lastMsgTime;
    });
    result = result.filter((item) => item.sessionId);

    console.log('queryConversationItems', result);
    if (isManage && !result.find((item) => item.userType === constant.userType.manage)) {
      const manageSession = {
        sessionId: imId,
        ownerId: imId,
        userType: constant.userType.manage,
        unReadNum: 0,
      };
      chatMessageUtil.handleSession(manageSession);
      result.splice(0, 0, manageSession);
    }
    /*if (!result.length) {
      result.push({
        sessionId: 1,
        ownerId: 1,
        isGroup: 0,
        title: '张三',
        content: '你好',
        lastMsgTime: Date.now(),
      });
    }*/
    await this.props.imAction.saveConversationItems(result);

    const { currentSessionList } = this.onSearch(result);
    return {
      result: currentSessionList,
      totalCount: currentSessionList.length,
    };
  };

  handleLoadResponse = async ({ result }) => {
    return {
      currentTotalCount: result.length,
      totalCount: result.length,
    };
  };

  // 输入搜索内容
  onChangeText = (text) =>
    this.setState({ searchValue: text }, () => {
      if (!text) {
        this.onSearchDebounce();
      }
    });

  onSubmitEditing = () => {
    this.onSearchDebounce();
  };

  @Debounce(300)
  onSearchDebounce() {
    this.onSearch(this.state.sessionList);
  }

  onSearch = (sessionList = this.state.sessionList) => {
    const { searchValue } = this.state;
    let currentSessionList = searchValue?.trim()
      ? sessionList.filter((item) => item.title?.includes(searchValue))
      : sessionList;
    if (this.props.filterSessionList) {
      currentSessionList = this.props.filterSessionList(currentSessionList);
    }
    currentSessionList.forEach((item) => {
      item.key = item.key || `${item.sessionId}_${!!item.isGroup}`;
    });
    const state = {
      searchValue,
      sessionList,
      currentSessionList: currentSessionList.slice(),
    };
    this.setState(state);
    return state;
  };

  onDeleteChat = (item) => {
    this.setState({
      showAlert: true,
      deleteItem: item,
      alertContent: item.isGroup
        ? I18n.t('page_chat_op_delete_p2p')
        : I18n.t('page_chat_op_delete_group'),
    });
  };

  onDeleteChatConfirm = async () => {
    try {
      await this.props.imAction.deleteSession(this.state.deleteItem);
      const { imId } = this.props.userStore;
      const sessionId = `${imId}_${this.state.deleteItem.sessionId}`;
      await this.props.imAction.deleteConversationItems({ sessionId });
      this.setState({ showAlert: false });
    } catch (e) {
      logger.warn('onDeleteChatConfirm', e);
    }
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  renderAlert = () => {
    const { showAlert, alertContent } = this.state;
    if (!showAlert) return null;
    return (
      <AlertPro
        visible={showAlert}
        title={alertContent}
        textConfirm={I18n.t('op_confirm_title')}
        textCancel={I18n.t('op_cancel_title')}
        onConfirm={this.onDeleteChatConfirm}
        onCancel={this.onAlertCancel}
      />
    );
  };

  renderItem = ({ item, index }) => {
    return <ChatItem item={item} index={index} />;
  };

  renderHiddenItem = ({ item }, rowMap) => {
    if (item.isGPT) return null;
    const { style } = this;
    return (
      <View style={style.rowBack}>
        <Touchable
          style={[style.backRightBtn, style.backRightBtnRight]}
          onPress={() => {
            if (rowMap[item.key]) {
              // 处理删除某个item后，下个 item 会自动侧滑显示删除
              rowMap[item.key].closeRow();
            }
            this.onDeleteChat(item);
          }}
        >
          <Text style={style.backTextWhite}>{I18n.t('page_chat_message_delete')}</Text>
        </Touchable>
      </View>
    );
  };

  renderListHeaderComponent = () => {
    return <View style={{ paddingVertical: 5 }}>{this.props.renderListHeaderComponent?.()}</View>;
  };

  render() {
    const { style } = this;
    const { currentSessionList } = this.state;
    return (
      <View style={style.chatContainer}>
        <View style={style.subContainer}>
          {currentSessionList.length > 0 ? (
            <SwipeListView
              showsVerticalScrollIndicator={false}
              disableRightSwipe
              useFlatList
              data={currentSessionList}
              renderItem={this.renderItem}
              ListHeaderComponent={this.renderListHeaderComponent}
              renderHiddenItem={this.renderHiddenItem}
              rightOpenValue={-75}
              closeOnRowPress={true}
              closeOnRowBeginSwipe={true}
              closeOnScroll={true}
              useNativeDriver={false}
            />
          ) : (
            <NoData />
          )}
        </View>
        {this.renderAlert()}
        <ActionSheet page="chat" />
      </View>
    );
  }
}
