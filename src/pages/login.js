import React from 'react';
import { inject, observer } from 'mobx-react';
import {
  Platform,
  BackHandler,
  ToastAndroid,
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
  Alert,
  StatusBar,
  requireNativeComponent,
} from 'react-native';
import { Input, Button, Divider, Icon } from 'react-native-elements';
import { GoogleSignin, statusCodes } from 'react-native-google-signin';
import { StackActions, NavigationActions } from 'react-navigation';
import LinkedInModal from 'react-native-linkedin';
import EvilIcon from 'react-native-vector-icons/EvilIcons';
import FIcon from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-easy-toast';
import axios from 'axios';
import configs from '../configs';
import TooltipMenu from '../components/tooltipMenu';
import CountryCode from '../components/countryCode';
import { loginStyle, phColor, subTitleColor } from '../themes';
import I18n, { setLanguage } from '../i18n';
import res from '../res';
import LoadingModal from '../components/loadingModal';
import I18nUtil from '../util/I18nUtil';
import regExp from '../util/regExp';
import { normalizePhone } from '../common/special';
import { hasEar, statusBarHeight, headerHeight } from '../common';
import constant from '../store/constant';
import NativeView from '../components/NativeView';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';
import { AccessToken, LoginManager, LoginButton } from 'react-native-fbsdk-next';
import * as WeChat from 'react-native-wechat-lib';
import DeviceInfo from '../util/deviceInfo';
import resIcon from '../res';
import validateUtil from '../util/validateUtil';
import InputImageCaptchaModal from '../components/modal/inputImageCaptchaModal';

let timeout = null;
let keyBoardIsShow = false;

@inject('loginAction', 'jobAction', 'resumeAction', 'userAction', 'globalAction')
@observer
export default class Login extends React.Component {
  static navigationOptions = {
    headerShown: false,
    gestureEnabled: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      tabId: 1,
      region: constant.defaultRegion,
      regionCode: constant.defaultRegion.code,
      regionCodeLabel: constant.defaultRegion.label,
      phone: '',
      code: '',
      account: '',
      password: '',
      captcha: '',
      captchaImage: '',
      signature: '',
      sending: false,
      seconds: 60,
      isOpenSelectedCountryCode: false,
      showLoading: false,
      showWechat: false, //是否显示微信登录
    };
  }

  componentDidMount() {
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow);
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide);
    if (Platform.OS.toLowerCase() === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackAndroid);
    }

    // this.getWechatStatus();
    this.getCaptchaImage();
    GoogleSignin.configure({
      webClientId: '************-kvadn1bkhbj4oi7dl94r40epqh5s42fi.apps.googleusercontent.com',
      offlineAccess: true,
    });
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    if (timeout) {
      clearInterval(timeout);
    }
    BackHandler.removeEventListener('hardwareBackPress', this.onBackAndroid);
  }

  getWechatStatus = async () => {
    WeChat.registerApp('wx04b7054e189453d4', 'https://camhr.com/');
    const showWechat = await WeChat.isWXAppInstalled();
    if (showWechat) {
      this.setState({ showWechat });
    }
  };

  onLoadInfo = () => {
    this.props.resumeAction.getResumes();
    this.props.jobAction.getJobStatistics();
    this.props.userAction.getCurrUserInfo();
  };

  onResetNavigation = async () => {
    await this.props.globalAction.setEnterprise(false);
    this.onLoadInfo();

    const resetAction = StackActions.reset({
      index: 0,
      actions: [NavigationActions.navigate({ routeName: 'main' })],
    });
    this.props.navigation.dispatch(resetAction);
  };

  onBackAndroid = () => {
    // 禁用返回键
    if (this.props.navigation.isFocused()) {
      // 判断   该页面是否处于聚焦状态
      if (this.lastBackPressed && this.lastBackPressed + 2000 >= Date.now()) {
        BackHandler.exitApp(); // 直接退出APP
        return false;
      }
      this.lastBackPressed = Date.now();
      ToastAndroid.show(I18n.t('tips_exit'), ToastAndroid.SHORT, ToastAndroid.CENTER);
      return true;
    }
    // 回调函数onBackAndroid中的return true是必不可少的
    return false;
  };

  _keyboardDidShow() {
    keyBoardIsShow = true;
  }

  _keyboardDidHide() {
    keyBoardIsShow = false;
  }

  onLostBlur = () => {
    // 退出软件盘
    if (keyBoardIsShow) {
      Keyboard.dismiss();
    }
  };

  onCloseLogin = () => {
    this.props.navigation.navigate('main');
  };

  onSwitchLanguage = (language) => {
    // this.props.jobAction.clearCurrentCity();
    setLanguage(language);
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    global.emitter.emit('languageChange', true);
    this.forceUpdate();
  };

  onSwitchTab = (tabId) => {
    this.setState({ tabId });
  };

  setPhone = (text) => {
    this.setState({
      phone: text,
    });
  };

  setCode = (text) => {
    this.setState({
      code: text,
    });
  };

  setAccount = (text) => {
    this.setState({
      account: text,
    });
  };

  setToken = (text) => {
    this.setState({
      password: text,
    });
  };

  setCaptcha = (text) => {
    this.setState({
      captcha: text,
    });
  };

  openCountryCodeModal = (value) => {
    this.onLostBlur();
    this.setState({ isOpenSelectedCountryCode: value });
  };

  runSendCodeTimeout = () => {
    this.setState({ sending: true });
    timeout = setInterval(() => {
      const { seconds } = this.state;
      const remainSeconds = seconds - 1;
      this.setState({ seconds: remainSeconds });
      if (remainSeconds === 0) {
        this.setState({ seconds: 60, sending: false });
        clearInterval(timeout);
      }
    }, 1000);
  };

  sendCode = async (imageCaptcha) => {
    this.onLostBlur();
    try {
      const { phone, regionCode, region, sending } = this.state;
      if (sending) return;
      const mobile = validateUtil.validatePhone(phone, region);
      if (!mobile) return;
      const res = await this.inputImageCaptchaModal.sendImPhoneCode(
        {
          param: {
            mobile,
            regionCode,
            imageCaptcha,
          },
          onConfirm: this.sendCode,
        },
        this.props.loginAction.sendLoginCode
      );
      if (res && res.successful) {
        toast.show(I18n.t('page_login_op_sendcode_success'));
        this.runSendCodeTimeout();
      } else {
        if (res.message) {
          toast.show(res.message);
        } else {
          toast.show(I18n.t('page_login_op_sendcode_error'));
        }
      }
    } catch (e) {
      console.log('sendVerifyCode error', err);
      this.setState({ sending: false });
    }
  };

  login = () => {
    this.onLostBlur();
    this.doLogin();
  };

  doLogin = () => {
    if (this.state.tabId === 1) {
      this.loginByPhone();
    } else {
      this.loginByAccount();
    }
  };

  loginByPhone = () => {
    const { phone, code, regionCode } = this.state;
    if (phone.trim() === '' || !regExp.numberExtra.test(phone.trim())) {
      this.toast.show(I18n.t('page_login_op_phone_required'));
      return;
    }
    if (code.trim() === '') {
      this.toast.show(I18n.t('page_login_op_code_required'));
      return;
    }
    this.setState({ showLoading: true });
    this.props.loginAction
      .loginByPhone(normalizePhone(phone.trim(), regionCode.trim()), code.trim(), regionCode.trim())
      .then(
        () => {
          this.setState({ showLoading: false });
          this.onResetNavigation();
        },
        (err) => {
          this.setState({ showLoading: false });
          if (err && err.message) {
            this.toast.show(err.message);
          } else {
            this.toast.show(I18n.t('page_login_op_phone_login_error'));
          }
        }
      );
  };

  loginByAccount = () => {
    const { account, password, captcha, signature } = this.state;
    if (account.trim() === '') {
      this.toast.show(I18n.t('page_login_op_username_required'));
      return;
    }
    if (password.trim() === '') {
      this.toast.show(I18n.t('page_login_op_password_required'));
      return;
    }
    if (captcha.trim() === '') {
      this.toast.show(I18n.t('page_login_op_img_code_required'));
      return;
    }
    this.setState({ showLoading: true });
    this.props.loginAction
      .loginByAccount(account.trim(), password.trim(), captcha.trim(), signature)
      .then(
        () => {
          this.setState({ showLoading: false });
          this.onResetNavigation();
        },
        (err) => {
          console.log(err);
          this.getCaptchaImage();
          this.setState({ showLoading: false });
          if (err && err.message) {
            this.toast.show(err.message);
          } else {
            this.toast.show(I18n.t('page_login_op_email_login_error'));
          }
        }
      );
  };

  onSelectedCountryCode = (item) => {
    if (item) {
      this.setState({ region: item, regionCodeLabel: item.label, regionCode: item.code });
    }
    this.setState({ isOpenSelectedCountryCode: false });
  };

  facebookLogin = () => {
    LoginManager.logInWithPermissions(['public_profile']).then(
      (result) => {
        if (!result.isCancelled) {
          AccessToken.getCurrentAccessToken().then(
            (data) => {
              this.loginWithAccessToken('facebook', data.accessToken.toString());
            },
            () => {}
          );
        }
      },
      () => {}
    );
  };

  loginWithAccessToken = (playform, accessToken) => {
    this.setState({ showLoading: true });
    this.props.loginAction.thirdParyLogin(playform, accessToken).then(
      (ress) => {
        console.log('ress', ress);
        this.setState({ showLoading: false });
        if (ress && ress.message) {
          this.toast.show(ress.message);
        }
        this.onResetNavigation();
      },
      (err) => {
        console.log('err', err);
        this.setState({ showLoading: false });
        console.log(err);
        this.showResponseResult(err);
      }
    );
  };

  linkedinModalLogin = (result) => {
    if (result && result.access_token) {
      this.setState({ showLoading: true });
      this.loginWithAccessToken('linkedin', result.access_token);
    }
  };

  wechatLogin = () => {
    WeChat.sendAuthRequest('snsapi_userinfo', 'camhr').then(
      (result) => {
        if (result && result.code) {
          this.setState({ showLoading: true });
          this.props.loginAction.thirdParyLoginWithCode('wechat', result.code).then(
            (ress) => {
              this.setState({ showLoading: false });
              this.showResponseResult(ress);
              this.onResetNavigation();
            },
            (err) => {
              this.showResponseResult(err);
              this.setState({ showLoading: false });
            }
          );
        } else {
          this.setState({ showLoading: false });
        }
      },
      () => {}
    );
  };

  signIn = async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      console.log('userInfo', userInfo);
      // Alert.alert('Google登录成功\n', `用户名:${userInfo.user.name}\n邮箱:${userInfo.user.email}`);
      const tokens = await GoogleSignin.getTokens();
      if (tokens && tokens.accessToken) {
        this.setState({ showLoading: true });
        this.loginWithAccessToken('google', tokens.accessToken);
      } else {
        this.setState({ showLoading: false });
      }
    } catch (error) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('取消登录');
        // user cancelled the login flow
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // operation (f.e. sign in) is in progress already
        console.log('userInfo');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('服务不可用');
        // play services not available or outdated
      } else {
        // some other error happened
        Alert.alert(I18n.t('page_tips_withdraw_title'), error.toString());
      }
    }
  };

  onAppleLogin = (info) => {
    if (info.nativeEvent.success) {
      this.setState({ showLoading: true });
      this.loginWithAccessToken('apple', info.nativeEvent.success);
      // alert(info.nativeEvent.success);
    } else if (info.nativeEvent.error) {
      // alert(info.nativeEvent.error);
    }
  };

  showResponseResult = (data) => {
    if (data && data.message) {
      this.toast.show(data.message);
    }
  };

  getCaptchaImage = () => {
    axios.get(`${configs.serverURL}/captcha/image?format=base64`).then((result) => {
      this.setState({
        captchaImage: result.data.image,
        signature: result.data.signature,
      });
    });
  };

  renderPhoneForm = () => (
    <View style={loginStyle.tabContent}>
      <View style={[loginStyle.formGroup, loginStyle.formGroupLine]}>
        <View style={loginStyle.formPhoneSection}>
          <View style={loginStyle.formPhoneAreaWrap}>
            <TouchableOpacity
              style={loginStyle.formPhoneAreaTo}
              onPress={() => {
                this.openCountryCodeModal(true);
              }}
            >
              <Text style={loginStyle.formPhoneArea}>
                {this.state.regionCodeLabel}
                <EvilIcon name="chevron-down" size={20} color={subTitleColor} />
              </Text>
            </TouchableOpacity>
          </View>
          <View style={loginStyle.formPhoneInputWrap}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={loginStyle.formControl}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_login_ph_phone')}
              placeholderTextColor={phColor}
              onChangeText={this.setPhone}
              value={this.state.phone}
              maxLength={15}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>
      <View style={loginStyle.formGroup}>
        <View style={loginStyle.formSendCodeSection}>
          <View style={loginStyle.formSendCodeInputWrap}>
            <Input
              inputContainerStyle={loginStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_login_ph_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCode}
              value={this.state.code}
              maxLength={6}
              keyboardType="numeric"
            />
          </View>
          <View style={loginStyle.formSendCodeBtnWrap}>
            <Button
              title={
                this.state.sending
                  ? `${I18n.t('page_login_btn_sendcode')}(${this.state.seconds})`
                  : I18n.t('page_login_btn_sendcode')
              }
              titleStyle={{ color: '#fff', fontSize: 16 }}
              buttonStyle={loginStyle.btnSendCode}
              disabled={this.state.sending}
              onPress={this.sendCode}
            />
          </View>
        </View>
      </View>
    </View>
  );

  renderAccountForm = () => (
    <View style={loginStyle.tabContent}>
      <View style={[loginStyle.formGroup, loginStyle.formGroupLine]}>
        <Input
          autoCapitalize="none"
          inputContainerStyle={loginStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_login_ph_username')}
          placeholderTextColor={phColor}
          onChangeText={this.setAccount}
          // value={this.state.account}
          maxLength={255}
        />
      </View>
      <View style={[loginStyle.formGroup, loginStyle.formGroupLine]}>
        <Input
          secureTextEntry
          inputContainerStyle={loginStyle.formControl}
          inputStyle={{ fontSize: 14 }}
          placeholder={I18n.t('page_login_ph_password')}
          placeholderTextColor={phColor}
          onChangeText={this.setToken}
          value={this.state.password}
          maxLength={16}
        />
      </View>
      <View style={loginStyle.formGroup}>
        <View style={loginStyle.formSendCodeSection}>
          <View style={loginStyle.formSendCodeInputWrap}>
            <Input
              autoCapitalize="none"
              inputContainerStyle={loginStyle.formControlCode}
              inputStyle={{ fontSize: 14 }}
              placeholder={I18n.t('page_login_ph_img_code')}
              placeholderTextColor={phColor}
              onChangeText={this.setCaptcha}
              value={this.state.captcha}
              maxLength={5}
            />
          </View>
          <TouchableOpacity style={loginStyle.formSendCodeBtnWrap} onPress={this.getCaptchaImage}>
            <Image source={{ uri: this.state.captchaImage }} style={{ height: 50, width: 150 }} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  render() {
    const { tabId, isOpenSelectedCountryCode, showWechat } = this.state;
    const { navigation } = this.props;
    return (
      // View 用以适配iPhoneX
      <View style={loginStyle.page}>
        <StatusBar barStyle="light-content" translucent backgroundColor="transparent" />
        <ImageBackground source={res.bgLogin} style={{ flex: 1, backgroundColor: 'transparent' }}>
          <TouchableWithoutFeedback onPress={() => this.onLostBlur()}>
            <View style={[loginStyle.container]}>
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginTop: statusBarHeight,
                    height: headerHeight,
                    alignItems: 'center',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      this.onCloseLogin();
                    }}
                  >
                    <Icon type="ionicon" name="ios-close-circle-outline" size={32} color="#fff" />
                  </TouchableOpacity>
                  <View style={[loginStyle.languageSection, { width: 100 }]}>
                    <TooltipMenu
                      isModalOpen
                      componentWrapperStyle={loginStyle.languageTooltipWrap}
                      buttonComponent={
                        <View style={loginStyle.languageWrap}>
                          <View style={loginStyle.languageDropdown}>
                            <Image source={res.iconDown} style={loginStyle.iconSwitchLanguage} />
                          </View>
                          <View style={loginStyle.language}>
                            <Text style={loginStyle.languageText}>{I18n.t('language')}</Text>
                          </View>
                        </View>
                      }
                      items={[
                        {
                          label: I18n.t('language', { locale: 'zh' }),
                          icon: resIcon.cnFlag,
                          onPress: () => this.onSwitchLanguage('zh'),
                        },
                        {
                          label: I18n.t('language', { locale: 'en' }),
                          icon: resIcon.usFlag,
                          onPress: () => this.onSwitchLanguage('en'),
                        },
                        {
                          label: I18n.t('language', { locale: 'km' }),
                          icon: resIcon.khFlag,
                          onPress: () => this.onSwitchLanguage('km'),
                        },
                        {
                          label: I18n.t('language', { locale: 'vi' }),
                          icon: resIcon.vnFlag,
                          onPress: () => this.onSwitchLanguage('vi'),
                        },
                        {
                          label: I18n.t('language', { locale: 'th' }),
                          icon: resIcon.thFlag,
                          onPress: () => this.onSwitchLanguage('th'),
                        },
                        {
                          label: I18n.t('language', { locale: 'ko' }),
                          icon: resIcon.krFlag,
                          onPress: () => this.onSwitchLanguage('ko'),
                        },
                      ]}
                    />
                  </View>
                </View>
                <View style={loginStyle.titleSection}>
                  <Text style={loginStyle.title}>{I18n.t('page_login_text_title')}</Text>
                </View>
                <View style={[loginStyle.tabSection, { height: tabId === 1 ? 150 : 200 }]}>
                  <View style={loginStyle.tabHeader}>
                    <View style={tabId === 1 && loginStyle.tabActive}>
                      <Text
                        style={[loginStyle.tabTitle, tabId === 1 && loginStyle.tabActiveTitle]}
                        onPress={() => this.onSwitchTab(1)}
                      >
                        {I18n.t('page_login_tab_phone')}
                      </Text>
                    </View>
                    <View style={tabId === 2 && loginStyle.tabActive}>
                      <Text
                        style={[loginStyle.tabTitle, tabId === 2 && loginStyle.tabActiveTitle]}
                        onPress={() => this.onSwitchTab(2)}
                      >
                        {I18n.t('page_login_tab_password')}
                      </Text>
                    </View>
                  </View>
                  <View style={loginStyle.tabBody}>
                    {tabId === 1 && this.renderPhoneForm()}
                    {tabId === 2 && this.renderAccountForm()}
                  </View>
                </View>
                <View style={loginStyle.btnSection}>
                  <TouchableOpacity style={loginStyle.btnLogin} onPress={this.login}>
                    <Text style={loginStyle.btnLoginText}>{I18n.t('page_login_btn_login')}</Text>
                  </TouchableOpacity>
                </View>
                <View style={loginStyle.linkSection}>
                  <View>
                    <TouchableOpacity
                      onPress={() => {
                        navigation.navigate('forgotPassword');
                      }}
                    >
                      <Text style={loginStyle.forgotLink}>{I18n.t('page_login_link_forgot')}?</Text>
                    </TouchableOpacity>
                  </View>
                  <View>
                    <TouchableOpacity
                      onPress={() => {
                        navigation.navigate('registerPhone');
                      }}
                    >
                      <Text style={loginStyle.registerLink}>
                        {I18n.t('page_login_link_register')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              <View style={loginStyle.thirdLoginSection}>
                <View style={loginStyle.thirdLoginHeader}>
                  <Divider style={loginStyle.thirdTitleDivider} />
                  <Text style={loginStyle.thirdTitle}>Or</Text>
                  <Divider style={loginStyle.thirdTitleDivider} />
                </View>
                <View style={loginStyle.thirdBtnGroupWrap}>
                  {/* <LoginButton
                    onLoginFinished={(error, result) => {
                      console.log('error', error);
                      console.log('result', result);
                      if (error) {
                        console.log('login has error: ' + result.error);
                      } else if (result.isCancelled) {
                        console.log('login is cancelled.');
                      } else {
                        AccessToken.getCurrentAccessToken().then((data) => {
                          console.log(data.accessToken.toString());
                        });
                      }
                    }}
                    onLogoutFinished={() => console.log('logout.')}
                  /> */}
                  <TouchableOpacity style={loginStyle.facebook} onPress={this.facebookLogin}>
                    <FIcon name="facebook" size={30} color="#fff" />
                  </TouchableOpacity>
                  <LinkedInModal
                    ref={(ref) => {
                      this.linkedRef = ref;
                    }}
                    clientID="8150xzpfc1jk9i"
                    clientSecret="ot0HhNqShbAEJccm"
                    redirectUri="https://api.linkedin.com/v1/people"
                    onSuccess={(token) => {
                      console.log('tokentokentokentoken', token);
                      this.linkedinModalLogin(token);
                    }}
                    renderButton={() => {
                      return (
                        <TouchableOpacity onPress={() => this.linkedRef.open()}>
                          <FIcon name="linkedin" size={32} color="#fff" />
                        </TouchableOpacity>
                      );
                    }}
                  />

                  <TouchableOpacity style={loginStyle.facebook} onPress={this.signIn}>
                    <FIcon name="google" size={30} color="#fff" />
                  </TouchableOpacity>

                  {/* {showWechat ? (
                    <TouchableOpacity style={loginStyle.linkedin} onPress={this.wechatLogin}>
                      <FIcon name="wechat" size={30} color="#fff" />
                    </TouchableOpacity>
                  ) : null} */}

                  {global.IS_IOS && parseFloat(DeviceInfo.getSystemVersion(), 10) >= 13 ? (
                    <NativeView style={loginStyle.facebook} onClick={this.onAppleLogin}>
                      <Image source={res.appleLogin} style={{ height: 30, width: 30 }} />
                    </NativeView>
                  ) : null}
                </View>
                {/* <GoogleSigninButton
                  style={{ width: 48, height: 48 }}
                  size={GoogleSigninButton.Size.Icon}
                  color={GoogleSigninButton.Color.Dark}
                  onPress={this.signIn}
                  disabled={false}
                /> */}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </ImageBackground>
        <CountryCode isOpen={isOpenSelectedCountryCode} onSelected={this.onSelectedCountryCode} />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips />
        <InputImageCaptchaModal ref={(ref) => (this.inputImageCaptchaModal = ref)} />
      </View>
    );
  }
}
