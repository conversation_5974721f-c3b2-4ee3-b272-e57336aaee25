import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput } from 'react-native';
import { inject, observer } from 'mobx-react';
import BottomModal from '../../../../components/modal/bottomModal';
import { Button } from '../../../../components';
import I18n from '../../../../i18n';
import styles from '../../../../themes/enterprise';

function getComponentStyle(theme) {
  return {
    container: {
      paddingHorizontal: 20,
      paddingBottom: 15,
    },
    inputRow: {
      marginBottom: 20,
    },
    label: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
      fontWeight: theme.fontWeightMedium,
    },
    input: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      backgroundColor: '#fff',
    },
    radioRow: {
      marginBottom: 20,
    },
    radioGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    radioOption: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 30,
    },
    radioButton: {
      width: 18,
      height: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: '#E5E5E5',
      marginRight: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.stressColor,
    },
    radioButtonInner: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.stressColor,
    },
    radioText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    button: {
      flex: 1,
      marginHorizontal: 5,
    },
  };
}

/**
 * 聊天设置弹出框组件
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class ChatSettingsModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
      imUsername: '',
      imPassword: '',
      chatStatus: 'open', // 'open' 或 'disabled'
    };
  }

  show = (data = {}) => {
    this.setState({
      isVisible: true,
      imUsername: data.imUsername || '',
      imPassword: data.imPassword || '',
      chatStatus: data.chatStatus || 'open',
    });
  };

  hide = () => {
    this.setState({ isVisible: false });
  };

  onChangeChatAccount = (text) => {
    this.setState({ imUsername: text });
  };

  onChangeChatPassword = (text) => {
    this.setState({ imPassword: text });
  };

  onChangeChatStatus = (status) => {
    this.setState({ chatStatus: status });
  };

  onConfirm = () => {
    const { imUsername, imPassword, chatStatus } = this.state;

    // 验证聊天账号
    if (!imUsername.trim()) {
      global.toast.show(I18n.t('page_chat_ph_chat_account_empty'));
      return;
    }

    // 调用父组件的确认回调
    if (this.props.onConfirm) {
      this.props.onConfirm({
        imUsername: imUsername.trim(),
        imPassword: imPassword.trim(),
        chatStatus,
      });
    }

    this.hide();
  };

  onCancel = () => {
    if (this.props.onCancel) {
      this.props.onCancel();
    }
    this.hide();
  };

  renderRadioButton = (value, label) => {
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const isSelected = this.state.chatStatus === value;

    return (
      <TouchableOpacity style={style.radioOption} onPress={() => this.onChangeChatStatus(value)}>
        <View style={[style.radioButton, isSelected && style.radioButtonSelected]}>
          {isSelected && <View style={style.radioButtonInner} />}
        </View>
        <Text style={style.radioText}>{label}</Text>
      </TouchableOpacity>
    );
  };

  render() {
    const { isVisible, imUsername, imPassword } = this.state;
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const contentHeight = 320;

    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_chat_set_title')}
        showCancel={false}
        contentHeight={contentHeight}
        keyboardShouldPersistTaps="always"
        onClosed={this.hide}
        isOpen={isVisible}
        useScrollContent={false}
        showBottomView={false}
      >
        <View style={style.container}>
          {/* 聊天账号输入 */}
          <View style={style.inputRow}>
            <Text style={style.label}>{I18n.t('page_chat_text_chat_account')}:</Text>
            <TextInput
              ref={(ref) => (this.chatAccountInput = ref)}
              style={style.input}
              placeholder={I18n.t('page_chat_ph_chat_account')}
              defaultValue={imUsername}
              onChangeText={this.onChangeChatAccount}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="next"
              selectionColor="#EF3D48"
              onSubmitEditing={() => this.chatPasswordInput?.focus()}
              maxLength={31}
            />
          </View>

          {/* 聊天密码输入 */}
          <View style={style.inputRow}>
            <Text style={style.label}>{I18n.t('page_chat_text_set_chat_password')}:</Text>
            <TextInput
              ref={(ref) => (this.chatPasswordInput = ref)}
              style={style.input}
              placeholder={I18n.t('page_login_op_password_required')}
              defaultValue={imPassword}
              onChangeText={this.onChangeChatPassword}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              returnKeyType="done"
              selectionColor="#EF3D48"
              onSubmitEditing={this.onConfirm}
              maxLength={31}
            />
          </View>

          {/* 聊天状态选择 */}
          <View style={style.radioRow}>
            <Text style={style.label}>{I18n.t('page_chat_text_chat_status')}:</Text>
            <View style={style.radioGroup}>
              {this.renderRadioButton('open', I18n.t('page_chat_text_chat_open'))}
              {this.renderRadioButton('disabled', I18n.t('page_chat_text_chat_open_disabled'))}
            </View>
          </View>

          {/* 按钮组 */}
          <View style={style.buttonContainer}>
            <Button
              title={I18n.t('op_cancel_title')}
              onPress={this.onCancel}
              btnSize="md"
              outline
              btnType="reset"
              containerStyle={style.button}
            />
            <Button
              title={I18n.t('op_confirm_title')}
              onPress={this.onConfirm}
              btnSize="md"
              btnType="primary"
              containerStyle={style.button}
            />
          </View>
        </View>
      </BottomModal>
    );
  }
}
