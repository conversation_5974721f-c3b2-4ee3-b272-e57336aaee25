import React from 'react';
import { inject, observer } from 'mobx-react';
import { Text, Touchable, View, Header, Image, BaseComponent, Alert } from '../../../components';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import resIcon from '../../../res';
import PageFlatList from '../../../components/list/pageFlatList';
import NavigationService from '../../../navigationService';
import { hasEar } from '../../../common';
import ChatSettingsModal from './components/ChatSettingsModal';

function getComponentStyle(theme) {
  return {
    container: {
      flex: 1,
      backgroundColor: theme.primaryBgColor,
    },
    userInfo: {
      paddingHorizontal: 18,
      paddingTop: 20,
    },
    nameText: {
      fontSize: theme.fontSizeIVX,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
      flexShrink: 10,
      marginBottom: 8,
    },
    itemContainer: {
      marginBottom: 15,
      marginHorizontal: 18,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#EEEEEE',
      position: 'relative',
    },
    itemHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 5,
    },
    labelText: {
      fontSize: theme.fontSizeL,
      color: theme.titleFontColor,
      fontWeight: theme.fontWeightBold,
      flexShrink: 10,
      marginRight: 10,
    },
    btnBox: {
      minWidth: 74,
      height: 32,
      borderRadius: 5,
      borderWidth: 1,
      paddingHorizontal: 4,
      borderColor: '#EF3D48',
    },
    btnText: {
      fontSize: theme.fontSizeM,
      color: '#EF3D48',
      lineHeight: 32,
      textAlign: 'center',
      lineHeight: 29,
    },
    jobText: {
      fontSize: theme.fontSizeM,
      color: '#484848',
      marginBottom: 10,
    },
    valueTextBox: {
      flexShrink: 10,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
    },
    valueText: {
      fontSize: theme.fontSizeL,
      color: '#484848',
      lineHeight: 20,
      marginLeft: 8,
      flexShrink: 10,
    },
    bottomContainer: {
      flex: 0,
      paddingVertical: 20,
      paddingBottom: hasEar ? 34 : 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
    },
    lineH: {
      width: 1,
      height: 23,
      backgroundColor: '#cccccc',
    },
    bottomText: {
      fontSize: theme.fontSizeM,
      color: '#3299FF',
      fontWeight: theme.fontWeightBold,
    },
  };
}

/**
 * 联系人
 */
@inject('jobStore', 'companyAction')
@observer
export default class JobContact extends BaseComponent {
  style = getComponentStyle(styles.get('theme'));

  constructor(props) {
    super(props);
    this.isCompany = props.navigation.state.params?.isCompany;
    this.state = {
      isManager: false,
    };
  }

  componentDidMount() {}

  initPageFlatList = (ref) => (this.pageFlatList = ref);

  onRefresh = () => this.pageFlatList.onRefresh();

  loadData = async (page) => {
    try {
      const param = {
        page,
        size: 6,
      };
      const data = this.props.companyAction.queryContacts(param);

      return data;
    } catch (error) {
      return { totalCount: 0, result: [] };
    }
  };

  onItemPress = async (item) => {
    const { isManager } = this.state;
    if (isManager) {
      Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_job_text_delete_contact'), [
        {
          text: I18n.t('page_setting_cancel_text'),
          onPress: () => {},
        },
        {
          text: I18n.t('page_job_btn_coonfirm'),
          onPress: () => this.onDeleteContact(item),
        },
      ]);
      return;
    }
    const { callback } = this.props.navigation.state.params || {};
    callback?.(item);
    NavigationService.goBack();
  };

  onImBtnClick = async (item) => {
    this.showChatSettingsModal(item);
  };

  onDeleteContact = async (item) => {
    try {
      this.showGlobalLoading();
      const res = await this.props.companyAction.deleteContact(item.id);
      if (res && res.successful) {
        this.showRequestResult(I18n.t('page_job_text_delete_address_success'));
        this.onRefresh();
      } else {
        this.showRequestResult();
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onDetail = (item) => {
    NavigationService.navigate('jobContactAdd', {
      item,
      callback: this.onRefresh,
    });
  };

  onManagerContact = () => {
    this.setState({
      isManager: !this.state.isManager,
    });
  };

  onAddContact = () => {
    NavigationService.navigate('jobContactAdd', {
      callback: this.onRefresh,
    });
  };

  showChatSettingsModal = (item) => {
    this.currentContactItem = item;
    this.chatSettingsModal.wrappedInstance.show({
      imUsername: item.imUsername || '',
      imPassword: item.imPassword || '',
      chatStatus: item.imStatus == 1 ? 'open' : 'disabled',
    });
  };

  onChatSettingsConfirm = async (data) => {
    try {
      this.showGlobalLoading();

      const { editContact } = this.props.companyAction;
      const params = {
        id: this.currentContactItem.id,
        imUsername: data.imUsername,
        imPassword: data.imPassword,
        imStatus: data.chatStatus == 'open' ? 1 : 0,
      };
      const res = await editContact(params);

      if (res && res.successful) {
        this.showRequestResult(I18n.t('msg_save_success'));
        this.onRefresh();
      } else {
        this.showRequestResult(I18n.t('msg_save_fail'));
      }
    } catch (error) {
      this.showRequestResult(error);
    }
  };

  onChatSettingsCancel = () => {
    // 取消操作，不做任何处理
  };

  renderItem = ({ item }) => {
    const { style } = this;
    const { isManager } = this.state;
    return (
      <Touchable style={style.itemContainer} onPress={() => this.onDetail(item)}>
        <View style={style.itemHeaderContainer}>
          <Text style={style.labelText}>{item.name} </Text>
          <Touchable
            style={[
              style.btnBox,
              isManager
                ? { backgroundColor: '#EF3D48' }
                : !this.isCompany
                ? {}
                : { borderWidth: 0 },
            ]}
            onPress={() => this.onItemPress(item)}
          >
            <Text style={[style.btnText, isManager ? { color: '#fff' } : {}]}>
              {this.state.isManager
                ? I18n.t('page_resume_annex_delete')
                : !this.isCompany
                ? I18n.t('page_job_text_select')
                : ''}
            </Text>
          </Touchable>
        </View>
        <Text style={style.jobText} textType="amount">
          {item.departmentId?.label || ''} {item.positionId?.label || ''}
        </Text>
        {item.telephone ? (
          <View style={style.valueTextBox}>
            <Image source={resIcon.resumeInfo5Enterprise} />
            <Text style={style.valueText} textType="amount">
              {item.telephone}
            </Text>
          </View>
        ) : null}
        {item.email ? (
          <View style={style.valueTextBox}>
            <Image source={resIcon.resumeInfo6Enterprise} />
            <Text style={style.valueText} textType="amount">
              {item.email}
            </Text>
          </View>
        ) : null}
        <Touchable
          style={[
            style.btnBox,
            { backgroundColor: '#EF3D48', position: 'absolute', right: 0, top: 44 },
          ]}
          onPress={() => this.onImBtnClick(item)}
        >
          <Text style={[style.btnText, { color: '#fff' }]}>
            {!item?.imUsername
              ? I18n.t('page_chat_set_title')
              : item.imStatus == 1
              ? I18n.t('page_chat_text_enable_chat')
              : I18n.t('page_chat_text_disable_chat')}
          </Text>
        </Touchable>
      </Touchable>
    );
  };

  render() {
    const { style } = this;
    return (
      <View style={style.container}>
        <Header theme="dark" />
        <View style={style.userInfo}>
          <Text style={style.nameText}>
            {this.isCompany
              ? I18n.t('page_company_text_set_contact')
              : I18n.t('page_company_text_select_contact')}
          </Text>
        </View>

        <PageFlatList
          ref={this.initPageFlatList}
          loadData={this.loadData}
          renderItem={this.renderItem}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={<View style={{ height: 20 }} />}
        />
        <View style={style.bottomContainer}>
          <Touchable onPress={this.onManagerContact}>
            <Text style={style.bottomText}>
              {' '}
              {this.state.isManager ? I18n.t('op_complete_title') : I18n.t('op_manage_title')}
            </Text>
          </Touchable>
          <View style={style.lineH} />
          <Touchable onPress={this.onAddContact}>
            <Text style={style.bottomText}>+{I18n.t('page_job_text_add_text')}</Text>
          </Touchable>
        </View>

        {/* 聊天设置弹出框 */}
        <ChatSettingsModal
          ref={(ref) => (this.chatSettingsModal = ref)}
          onConfirm={this.onChatSettingsConfirm}
          onCancel={this.onChatSettingsCancel}
        />
      </View>
    );
  }
}
