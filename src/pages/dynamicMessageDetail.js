import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import { <PERSON><PERSON>, Header } from 'react-native-elements';
import ActionSheet from 'react-native-actionsheet';
import {
  Text,
  View,
  TouchableOpacity,
  Alert,
  TouchableHighlight,
  FlatList,
  ScrollView,
} from 'react-native';
import { dynamicDetailStyle, bgColor, headerStyle, baseBlueColor } from '../themes';
import { getAvatarSource } from '../res';
import util from '../util';
import I18n from '../i18n';
import Session from '../api/session';
import GoBack from '../components/goback';
import LoadingModal from '../components/loadingModal';
import Image from '../components/image';

@inject('dynamicAction', 'dynamicStore', 'userAction', 'personStore')
@observer
export default class dynamicMessageDetail extends Component {
  constructor(props) {
    super(props);
    this.isLoadingMoreComments = false;
    this.state = {
      commentsPage: 1,
      hasMoreComment: false,
      commentRefresh: false,
      selectComment: null,
      showLoading: true,
    };
  }

  componentDidMount() {
    this.getComments();
    this.onGetTwitterDetail();
  }

  componentWillUnmount() {
    this.goBack();
    this.props.dynamicAction.resetComments();
    this.props.userAction.resetTwitterDetail();
  }

  onGetTwitterDetail = () => {
    const { info } = this.props.navigation.state.params;
    this.props.userAction.getTwitterDetail(info.id);
  };

  onCommentRefresh = async () => {
    this.setState({ commentRefresh: true });
    this.state.page = 1;
    await this.getComments();
  };

  onLoadMoreComments = async () => {
    if (this.state.hasMoreComment && !this.isLoadingMoreComments) {
      this.state.commentsPage += 1;
      this.isLoadingMoreComments = true;
      await this.getComments();
      setTimeout(() => {
        this.isLoadingMoreComments = false;
      }, 0);
    }
  };

  onCommentClick = (item) => {
    this.setState({ selectComment: item });
    if (item.mine) {
      this.commentActionSheet.show();
    }
  };

  onActionSheetSelect = (index) => {
    if (index === 0) {
      this.onDeleteComment();
    }
  };

  onDeleteComment = async () => {
    const data = this.state.selectComment;
    const ress = await this.props.dynamicAction.deleteComment(data.twitterId, data.id);
    if (ress) {
      if (ress.message) {
        toast.show(ress.message);
      }
      if (ress.successful) {
        this.getComments();
      }
    }
  };

  onDeleteDynamic = (info) => {
    Alert.alert(I18n.t('page_dynamic_delete_tips'), '', [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: async () => {
          try {
            const ress = await this.props.dynamicAction.deleteDynamic(info.id);
            if (ress) {
              if (ress.message) {
                toast.show(ress.message);
              }
              if (ress.successful) {
                this.goBack();
              }
            }
          } catch (error) {
            console.log(error);
          }
        },
      },
    ]);
  };

  onAvatarsClick = () => {
    const { info } = this.props.navigation.state.params;
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { navigation } = this.props;
        if (info.mine) return;
        navigation.navigate('userProfile', {
          data: info,
        });
      } else {
        this.onShowAlert();
      }
    });
  };

  onToUserProfile = (item) => {
    const { navigation } = this.props;
    if (item.mine) return;
    navigation.push('userProfile', {
      data: item,
    });
  };

  getComments = async () => {
    const { info } = this.props.navigation.state.params;
    try {
      await this.props.dynamicAction.queryComments(info.id, {
        page: this.state.commentsPage,
        size: 10,
      });
      const { commentTotalCount, commentList } = this.props.dynamicStore;
      this.setState({
        hasMoreComment: commentList && commentList.length < parseFloat(commentTotalCount),
        commentRefresh: false,
        showLoading: false,
      });
    } catch (error) {
      this.setState({ commentRefresh: false, showLoading: false });
    }
  };

  goBack = async () => {
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.onRefreshDynamic) {
      await fuc.onRefreshDynamic();
      const { navigation } = this.props;
      navigation.goBack();
      return;
    }
    const { navigation } = this.props;
    navigation.goBack();
  };

  previewDetail = (i) => {
    const { info } = this.props.navigation.state.params;
    const { navigation } = this.props;
    const imgArr = [];
    info.images.forEach((item) => {
      imgArr.push({ url: item });
    });
    navigation.navigate('previewImage', { images: imgArr, index: i });
  };

  likeUserClick = (item) => {
    console.log(item);
    const { navigation } = this.props;
    if (item.mine) return;
    navigation.push('userProfile', {
      data: item,
      onReloadLikedUsers: () => {
        this.onGetLikedUsers();
      },
    });
  };

  renderCommentItemView({ item }) {
    return (
      // eslint-disable-next-line react/jsx-no-bind
      <TouchableOpacity key={item.id} onPress={this.onCommentClick.bind(this, item)}>
        <View style={dynamicDetailStyle.commentItemtContent}>
          <View style={dynamicDetailStyle.commentItemLeft}>
            <TouchableOpacity onPress={() => this.onToUserProfile(item)}>
              <Image source={getAvatarSource(item.avatar)} style={dynamicDetailStyle.commentIcon} />
            </TouchableOpacity>
          </View>
          <View style={dynamicDetailStyle.commentItemRight}>
            <View style={dynamicDetailStyle.commentItemtTitleContent}>
              <Text style={dynamicDetailStyle.commentName}>
                {item.userName ? item.userName : I18n.t('page_dynamic_title_ay_username')}
              </Text>
              <Text style={dynamicDetailStyle.commentTime}>
                {item.createAt_unixtime ? util.getDiffBetweenHanSimple(item.createAt_unixtime) : ''}
              </Text>
            </View>
            <Text style={dynamicDetailStyle.commentContent}>{item.content}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    const {
      dynamicStore: { commentList },
      navigation,
      personStore: { twitterDetail },
    } = this.props;
    return (
      <View style={dynamicDetailStyle.container}>
        <Header
          statusBarProps={{ barStyle: 'light-content', backgroundColor: '#2089DC' }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_dynamic_header_detail_title'),
            style: headerStyle.center,
          }}
          leftComponent={<GoBack navigation={navigation} />}
          innerContainerStyles={{ justifyContent: 'center' }}
        />
        <ScrollView>
          <View style={dynamicDetailStyle.top}>
            <TouchableOpacity
              onPress={() => {
                this.onAvatarsClick();
              }}
            >
              <Image
                source={getAvatarSource(
                  twitterDetail && twitterDetail.avatar ? twitterDetail.avatar : ''
                )}
                style={[dynamicDetailStyle.icon, { backgroundColor: '#fff' }]}
              />
            </TouchableOpacity>
            <View style={dynamicDetailStyle.rightContent}>
              <Text style={dynamicDetailStyle.title}>
                {twitterDetail && twitterDetail.userName
                  ? twitterDetail.userName
                  : I18n.t('page_dynamic_title_ay_username')}
              </Text>
              <Text selectable style={dynamicDetailStyle.content}>
                {twitterDetail && twitterDetail.content ? twitterDetail.content : ''}
              </Text>
              <View style={[dynamicDetailStyle.imagesContent, { marginBottom: 10 }]}>
                {twitterDetail &&
                  twitterDetail.images &&
                  twitterDetail.images.map((item, i) => (
                    <TouchableOpacity
                      key={`${i + 1}`}
                      onPress={() => {
                        this.previewDetail(i);
                      }}
                    >
                      <Image source={{ uri: item }} style={dynamicDetailStyle.image} />
                    </TouchableOpacity>
                  ))}
              </View>
              <View
                style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}
              >
                <Text numberOfLines={1} style={dynamicDetailStyle.time}>
                  {twitterDetail && twitterDetail.createAt_unixtime
                    ? util.getDiffBetweenHan(twitterDetail.createAt_unixtime)
                    : ''}
                </Text>
                {twitterDetail && twitterDetail.mine ? (
                  <TouchableHighlight
                    underlayColor="transparent"
                    onPress={() => {
                      this.onDeleteDynamic(twitterDetail);
                    }}
                  >
                    <Text style={{ marginLeft: 10, fontSize: 14, color: baseBlueColor }}>
                      {I18n.t('page_dynamic_detail_delete')}
                    </Text>
                  </TouchableHighlight>
                ) : (
                  <Text style={{ display: 'none' }} />
                )}
              </View>
            </View>
          </View>
          <View style={{ marginTop: 10, backgroundColor: bgColor, marginHorizontal: 12 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', flex: 1 }}>
              {commentList && commentList.length > 0 ? (
                <View style={{ marginHorizontal: 12, marginTop: 10 }}>
                  <Icon name="message-square" type="feather" size={20} color={baseBlueColor} />
                </View>
              ) : (
                <View style={{ display: 'none' }} />
              )}
              <FlatList
                data={commentList && commentList.slice()}
                // eslint-disable-next-line react/jsx-no-bind
                renderItem={this.renderCommentItemView.bind(this)}
                keyExtractor={(item, index) => index + item}
                showsVerticalScrollIndicator={false}
                horizontal={false}
                refreshing={this.state.commentRefresh}
                onRefresh={this.onCommentRefresh}
                // ListFooterComponent={this.renderCommentFooter}
                onEndReachedThreshold={0.1}
                onEndReached={() => this.onLoadMoreComments()}
                ItemSeparatorComponent={() => (
                  <View style={{ height: 1, backgroundColor: '#eee', marginHorizontal: 15 }} />
                )}
              />
            </View>
          </View>
        </ScrollView>
        <ActionSheet
          ref={(ref) => {
            this.commentActionSheet = ref;
          }}
          options={[I18n.t('page_resume_btn_del'), I18n.t('page_sheet_label_cancel')]}
          cancelButtonIndex={1}
          tintColor="red"
          onPress={this.onActionSheetSelect}
        />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
